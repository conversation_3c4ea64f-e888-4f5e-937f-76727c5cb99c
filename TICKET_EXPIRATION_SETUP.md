# Hướng dẫn thiết lập tự động cập nhật vé hết hạn

## Tổng quan

Hệ thống tự động cập nhật vé hết hạn bao gồm 3 phương pháp:

1. **Client-side Timer**: Ki<PERSON><PERSON> tra mỗi 10 phút từ ứng dụng
2. **Firebase Functions**: Tự động chạy mỗi giờ trên server
3. **Manual Trigger**: <PERSON><PERSON> có thể kích hoạt thủ công

## 1. Client-side Timer (Đã triển khai)

### Tính năng:
- Tự động kiểm tra vé hết hạn mỗi 10 phút
- Chạy khi ứng dụng đang hoạt động
- Fallback khi server không khả dụng

### Files liên quan:
- `lib/services/ticket_expiration_service.dart`
- `lib/services/ticket_service.dart`
- `lib/controllers/ticket_controller.dart`

## 2. Firebase Functions (Cần triển khai)

### Bước 1: Cài đặt Firebase CLI
```bash
npm install -g firebase-tools
firebase login
```

### Bước 2: Khởi tạo Functions
```bash
cd your-project-directory
firebase init functions
```

### Bước 3: Cài đặt dependencies
```bash
cd functions
npm install
```

### Bước 4: Deploy Functions
```bash
firebase deploy --only functions
```

### Functions đã tạo:
1. **updateExpiredTickets**: Chạy tự động mỗi giờ
2. **manualUpdateExpiredTickets**: Gọi thủ công từ admin
3. **getExpiredTicketsStats**: Lấy thống kê vé hết hạn

### Cấu hình timezone:
Functions được cấu hình chạy theo giờ Việt Nam (Asia/Ho_Chi_Minh)

## 3. Admin Interface (Đã triển khai)

### Truy cập:
- Đăng nhập admin → Admin Dashboard → "Ticket Expiration"
- Route: `/admin/ticket-expiration`

### Tính năng:
- Xem thống kê vé hết hạn
- Kiểm tra thủ công (client-side + server-side)
- Cập nhật từ server
- Làm mới trạng thái

## 4. Cấu hình bảo mật

### Firestore Rules
Thêm rules cho admin/developer:
```javascript
// Trong firestore.rules
match /tickets/{ticketId} {
  allow read, write: if request.auth != null && 
    (request.auth.token.admin == true || request.auth.token.developer == true);
}
```

### Custom Claims
Đảm bảo admin/developer có custom claims:
```javascript
// Set custom claims cho admin
admin.auth().setCustomUserClaims(uid, {
  admin: true
});

// Set custom claims cho developer  
admin.auth().setCustomUserClaims(uid, {
  developer: true,
  admin: true
});
```

## 5. Monitoring và Logging

### Firebase Console:
- Functions → Logs: Xem logs của scheduled functions
- Firestore → Usage: Monitor read/write operations

### App Logs:
- Client-side logs trong console
- Server-side logs trong Firebase Functions

## 6. Testing

### Test Client-side:
1. Tạo vé với ngày/giờ trong quá khứ
2. Chờ 10 phút hoặc gọi `checkExpiredTickets()` thủ công
3. Kiểm tra status vé đã chuyển thành 'expired'

### Test Server-side:
1. Deploy functions lên Firebase
2. Sử dụng admin interface để test
3. Kiểm tra logs trong Firebase Console

### Test Manual Trigger:
1. Vào admin interface
2. Click "Cập nhật từ server"
3. Kiểm tra kết quả

## 7. Troubleshooting

### Lỗi thường gặp:

1. **Functions không deploy được**:
   - Kiểm tra Firebase CLI version
   - Đảm bảo project ID đúng
   - Kiểm tra billing account

2. **Permission denied**:
   - Kiểm tra custom claims
   - Kiểm tra Firestore rules
   - Đảm bảo user đã đăng nhập

3. **Client-side không hoạt động**:
   - Kiểm tra service đã được khởi tạo trong main.dart
   - Kiểm tra timer interval
   - Xem logs trong console

## 8. Tối ưu hóa

### Performance:
- Batch operations cho nhiều vé
- Index Firestore cho queries
- Limit số lượng vé xử lý mỗi lần

### Cost:
- Scheduled functions chạy mỗi giờ thay vì mỗi phút
- Client-side timer giảm tải cho server
- Efficient queries với proper indexing

## 9. Backup và Recovery

### Backup:
- Firestore tự động backup
- Export data định kỳ nếu cần

### Recovery:
- Rollback functions nếu có lỗi
- Restore từ backup nếu cần thiết

## 10. Future Enhancements

### Có thể thêm:
- Email notification khi vé sắp hết hạn
- Push notifications
- Analytics về vé hết hạn
- Automatic refund processing
- Integration với payment gateway
