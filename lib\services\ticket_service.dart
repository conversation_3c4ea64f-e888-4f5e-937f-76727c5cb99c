import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/ticket_model.dart';

class TicketService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final String _collection = 'tickets';

  // Create a new ticket
  Future<Ticket> createTicket(Ticket ticket) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Generate booking code if not provided
      String bookingCode = ticket.bookingCode;
      if (bookingCode.isEmpty) {
        bookingCode = _generateBookingCode();
      }

      final ticketData = ticket.copyWith(
        userId: user.uid,
        bookingCode: bookingCode,
        purchaseDate: DateTime.now(),
      );

      final docRef =
          await _firestore.collection(_collection).add(ticketData.toJson());

      return ticketData.copyWith(id: docRef.id);
    } catch (e) {
      throw Exception('Failed to create ticket: $e');
    }
  }

  // Get tickets for current user
  Future<List<Ticket>> getUserTickets() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final snapshot = await _firestore
          .collection(_collection)
          .where('userId', isEqualTo: user.uid)
          .get();

      final tickets =
          snapshot.docs.map((doc) => Ticket.fromFirestore(doc)).toList();

      // Sort by purchase date in memory
      tickets.sort((a, b) => b.purchaseDate.compareTo(a.purchaseDate));

      return tickets;
    } catch (e) {
      throw Exception('Failed to get user tickets: $e');
    }
  }

  // Get ticket by ID
  Future<Ticket?> getTicketById(String ticketId) async {
    try {
      final doc = await _firestore.collection(_collection).doc(ticketId).get();

      if (doc.exists) {
        return Ticket.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get ticket: $e');
    }
  }

  // Get ticket by booking code
  Future<Ticket?> getTicketByBookingCode(String bookingCode) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('bookingCode', isEqualTo: bookingCode)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        return Ticket.fromFirestore(snapshot.docs.first);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get ticket by booking code: $e');
    }
  }

  // Update ticket status
  Future<void> updateTicketStatus(String ticketId, TicketStatus status) async {
    try {
      final updateData = <String, dynamic>{
        'status': status.name,
        'updatedAt': Timestamp.now(),
      };

      if (status == TicketStatus.used) {
        updateData['usedAt'] = Timestamp.now();
      } else if (status == TicketStatus.cancelled) {
        updateData['cancelledAt'] = Timestamp.now();
      }

      await _firestore.collection(_collection).doc(ticketId).update(updateData);
    } catch (e) {
      throw Exception('Failed to update ticket status: $e');
    }
  }

  // Cancel ticket
  Future<void> cancelTicket(String ticketId, double refundAmount) async {
    try {
      await _firestore.collection(_collection).doc(ticketId).update({
        'status': TicketStatus.cancelled.name,
        'cancelledAt': Timestamp.now(),
        'refundAmount': refundAmount,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to cancel ticket: $e');
    }
  }

  // Use ticket (mark as used)
  Future<void> useTicket(String ticketId) async {
    try {
      await updateTicketStatus(ticketId, TicketStatus.used);
    } catch (e) {
      throw Exception('Failed to use ticket: $e');
    }
  }

  // Check and update expired tickets
  Future<void> checkAndUpdateExpiredTickets() async {
    try {
      final now = DateTime.now();
      final currentDateString =
          '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
      final currentTimeString =
          '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';

      // Get all confirmed tickets that might be expired
      final snapshot = await _firestore
          .collection(_collection)
          .where('status', isEqualTo: TicketStatus.confirmed.name)
          .where('date', isLessThan: currentDateString)
          .get();

      // Also check tickets for today that have passed their showtime
      final todaySnapshot = await _firestore
          .collection(_collection)
          .where('status', isEqualTo: TicketStatus.confirmed.name)
          .where('date', isEqualTo: currentDateString)
          .where('time', isLessThan: currentTimeString)
          .get();

      final expiredTickets = [...snapshot.docs, ...todaySnapshot.docs];

      for (final doc in expiredTickets) {
        try {
          await updateTicketStatus(doc.id, TicketStatus.expired);
          print('Updated expired ticket: ${doc.id}');
        } catch (e) {
          print('Error updating ticket ${doc.id}: $e');
        }
      }

      if (expiredTickets.isNotEmpty) {
        print('Updated ${expiredTickets.length} expired tickets');
      }
    } catch (e) {
      print('Error checking expired tickets: $e');
    }
  }

  // Get tickets by status
  Future<List<Ticket>> getTicketsByStatus(TicketStatus status) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final snapshot = await _firestore
          .collection(_collection)
          .where('userId', isEqualTo: user.uid)
          .where('status', isEqualTo: status.name)
          .orderBy('purchaseDate', descending: true)
          .get();

      return snapshot.docs.map((doc) => Ticket.fromFirestore(doc)).toList();
    } catch (e) {
      throw Exception('Failed to get tickets by status: $e');
    }
  }

  // Get upcoming tickets (confirmed tickets for future dates)
  Future<List<Ticket>> getUpcomingTickets() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final today = DateTime.now();
      final todayString =
          '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}';

      final snapshot = await _firestore
          .collection(_collection)
          .where('userId', isEqualTo: user.uid)
          .where('status', isEqualTo: TicketStatus.confirmed.name)
          .where('date', isGreaterThanOrEqualTo: todayString)
          .orderBy('date')
          .orderBy('time')
          .get();

      return snapshot.docs.map((doc) => Ticket.fromFirestore(doc)).toList();
    } catch (e) {
      throw Exception('Failed to get upcoming tickets: $e');
    }
  }

  // Get ticket history (past tickets)
  Future<List<Ticket>> getTicketHistory() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final today = DateTime.now();
      final todayString =
          '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}';

      final snapshot = await _firestore
          .collection(_collection)
          .where('userId', isEqualTo: user.uid)
          .where('date', isLessThan: todayString)
          .orderBy('date', descending: true)
          .orderBy('time', descending: true)
          .get();

      return snapshot.docs.map((doc) => Ticket.fromFirestore(doc)).toList();
    } catch (e) {
      throw Exception('Failed to get ticket history: $e');
    }
  }

  // Generate unique booking code
  String _generateBookingCode() {
    final now = DateTime.now();
    final timestamp = now.millisecondsSinceEpoch.toString();
    final random = (timestamp.hashCode % 10000).toString().padLeft(4, '0');
    return 'DP${timestamp.substring(timestamp.length - 6)}$random';
  }

  // Validate ticket for entry
  Future<bool> validateTicketForEntry(String bookingCode) async {
    try {
      final ticket = await getTicketByBookingCode(bookingCode);

      if (ticket == null) {
        return false;
      }

      // Check if ticket is confirmed and not used
      if (ticket.status != TicketStatus.confirmed) {
        return false;
      }

      // Check if ticket is for today or future
      final today = DateTime.now();
      final ticketDate = DateTime.parse(ticket.date);

      if (ticketDate.isBefore(DateTime(today.year, today.month, today.day))) {
        return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  // Get ticket statistics for user
  Future<Map<String, int>> getUserTicketStats() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final snapshot = await _firestore
          .collection(_collection)
          .where('userId', isEqualTo: user.uid)
          .get();

      final tickets =
          snapshot.docs.map((doc) => Ticket.fromFirestore(doc)).toList();

      return {
        'total': tickets.length,
        'confirmed':
            tickets.where((t) => t.status == TicketStatus.confirmed).length,
        'used': tickets.where((t) => t.status == TicketStatus.used).length,
        'cancelled':
            tickets.where((t) => t.status == TicketStatus.cancelled).length,
      };
    } catch (e) {
      throw Exception('Failed to get ticket statistics: $e');
    }
  }

  // Stream user tickets (real-time updates)
  Stream<List<Ticket>> streamUserTickets() {
    final user = _auth.currentUser;
    if (user == null) {
      return Stream.error('User not authenticated');
    }

    return _firestore
        .collection(_collection)
        .where('userId', isEqualTo: user.uid)
        .orderBy('purchaseDate', descending: true)
        .snapshots()
        .map((snapshot) =>
            snapshot.docs.map((doc) => Ticket.fromFirestore(doc)).toList());
  }
}
