import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:movie_finder/controllers/schedule_controller.dart';
import 'package:movie_finder/models/showtime_model.dart';
import 'package:movie_finder/models/movie_model.dart';
import 'package:movie_finder/view/admin/schedule_form_page.dart';

class ScheduleManagementPage extends StatefulWidget {
  const ScheduleManagementPage({Key? key}) : super(key: key);

  @override
  State<ScheduleManagementPage> createState() => _ScheduleManagementPageState();
}

class _ScheduleManagementPageState extends State<ScheduleManagementPage> {
  final ScheduleController _scheduleController = Get.find<ScheduleController>();
  final TextEditingController _searchController = TextEditingController();
  Timer? _debounceTimer;
  String _searchQuery = '';

  // Filter states
  String? _selectedDate;
  int? _selectedMovieId;
  bool _showFilters = false;

  @override
  void initState() {
    super.initState();
    _scheduleController.loadInitialData();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      setState(() {
        _searchQuery = _searchController.text.toLowerCase();
      });
    });
  }

  List<ShowtimeModel> _getFilteredShowtimes() {
    List<ShowtimeModel> filtered = _scheduleController.showtimes;

    // Filter by search query (theater name or movie title)
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((showtime) {
        final theater = _scheduleController.getTheaterById(showtime.theaterId);
        final movie = _scheduleController.getMovieById(showtime.movieId);

        final theaterName = (theater?.name ?? '').toLowerCase();
        final movieTitle = (movie?.title ?? '').toLowerCase();

        return theaterName.contains(_searchQuery) ||
            movieTitle.contains(_searchQuery);
      }).toList();
    }

    // Filter by selected date
    if (_selectedDate != null) {
      filtered = filtered.where((showtime) {
        return showtime.date == _selectedDate;
      }).toList();
    }

    // Filter by selected movie
    if (_selectedMovieId != null) {
      filtered = filtered.where((showtime) {
        return showtime.movieId == _selectedMovieId;
      }).toList();
    }

    return filtered;
  }

  List<String> _getAvailableDates() {
    final dates = _scheduleController.showtimes
        .map((showtime) => showtime.date)
        .toSet()
        .toList();
    dates.sort();
    return dates;
  }

  List<Movie> _getAvailableMovies() {
    final movieIds = _scheduleController.showtimes
        .map((showtime) => showtime.movieId)
        .toSet();

    return movieIds
        .map((id) => _scheduleController.getMovieById(id))
        .where((movie) => movie != null)
        .cast<Movie>()
        .toList()
      ..sort((a, b) => a.title.compareTo(b.title));
  }

  void _clearAllFilters() {
    setState(() {
      _searchQuery = '';
      _selectedDate = null;
      _selectedMovieId = null;
    });
    _searchController.clear();
  }

  bool get _hasActiveFilters {
    return _searchQuery.isNotEmpty ||
        _selectedDate != null ||
        _selectedMovieId != null;
  }

  String _buildFilterSummary() {
    List<String> filters = [];

    if (_searchQuery.isNotEmpty) {
      filters.add('Từ khóa: "$_searchQuery"');
    }

    if (_selectedDate != null) {
      filters.add('Ngày: $_selectedDate');
    }

    if (_selectedMovieId != null) {
      final movie = _scheduleController.getMovieById(_selectedMovieId!);
      filters.add('Phim: "${movie?.title ?? 'Không xác định'}"');
    }

    return 'Bộ lọc hiện tại: ${filters.join(', ')}';
  }

  Widget _buildFilterChip(String label, VoidCallback onRemove) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.2),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.blue.withOpacity(0.5)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: Text(
              label,
              style: GoogleFonts.mulish(
                color: Colors.blue,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 4),
          GestureDetector(
            onTap: onRemove,
            child: const Icon(
              Icons.close,
              size: 16,
              color: Colors.blue,
            ),
          ),
        ],
      ),
    );
  }

  String _getLoadingMessage() {
    if (_scheduleController.isLoadingTheaters.value) {
      return 'Đang tải danh sách rạp chiếu...';
    } else if (_scheduleController.isLoadingMovies.value) {
      return 'Đang tải danh sách phim...';
    } else if (_scheduleController.isLoadingScreens.value) {
      return 'Đang tải danh sách phòng chiếu...';
    } else if (_scheduleController.isLoadingShowtimes.value) {
      return 'Đang tải lịch chiếu...';
    }
    return 'Đang tải dữ liệu...';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xff1a1a2e),
      appBar: AppBar(
        title: Text(
          'Quản lý lịch chiếu',
          style: GoogleFonts.mulish(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xff16213e),
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () => _scheduleController.loadAllShowtimes(),
            icon: const Icon(Icons.refresh, color: Colors.white),
          ),
        ],
      ),
      body: Column(
        children: [
          // Header with add button
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Danh sách lịch chiếu',
                  style: GoogleFonts.mulish(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => _navigateToCreateSchedule(),
                  icon: const Icon(Icons.add),
                  label: Text(
                    'Thêm mới',
                    style: GoogleFonts.mulish(fontWeight: FontWeight.bold),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ),

          // Search Bar
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: TextField(
              controller: _searchController,
              autofocus: false, // Explicitly disable autofocus
              style: GoogleFonts.mulish(color: Colors.white),
              decoration: InputDecoration(
                hintText: 'Tìm kiếm theo tên rạp...',
                hintStyle: GoogleFonts.mulish(
                  color: Colors.white.withOpacity(0.6),
                ),
                prefixIcon: Icon(
                  Icons.search,
                  color: Colors.white.withOpacity(0.6),
                ),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                        },
                        icon: Icon(
                          Icons.clear,
                          color: Colors.white.withOpacity(0.6),
                        ),
                      )
                    : null,
                filled: true,
                fillColor: Colors.white.withOpacity(0.1),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Filter Section
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Filter Toggle Button
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.filter_list,
                          color: Colors.white.withOpacity(0.7),
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Bộ lọc',
                          style: GoogleFonts.mulish(
                            color: Colors.white.withOpacity(0.7),
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (_hasActiveFilters) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.blue,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Text(
                              '${(_searchQuery.isNotEmpty ? 1 : 0) + (_selectedDate != null ? 1 : 0) + (_selectedMovieId != null ? 1 : 0)}',
                              style: GoogleFonts.mulish(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    Row(
                      children: [
                        if (_hasActiveFilters)
                          TextButton(
                            onPressed: _clearAllFilters,
                            child: Text(
                              'Xóa tất cả',
                              style: GoogleFonts.mulish(
                                color: Colors.red,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        IconButton(
                          onPressed: () {
                            setState(() {
                              _showFilters = !_showFilters;
                            });
                          },
                          icon: Icon(
                            _showFilters
                                ? Icons.expand_less
                                : Icons.expand_more,
                            color: Colors.white.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                // Filter Options (Expandable)
                if (_showFilters) ...[
                  const SizedBox(height: 16),

                  // Date Filter
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Ngày chiếu:',
                              style: GoogleFonts.mulish(
                                color: Colors.white.withOpacity(0.8),
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Container(
                              width: double.infinity,
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 12),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.2),
                                ),
                              ),
                              child: DropdownButtonHideUnderline(
                                child: DropdownButton<String>(
                                  value: _selectedDate,
                                  hint: Text(
                                    'Chọn ngày',
                                    style: GoogleFonts.mulish(
                                      color: Colors.white.withOpacity(0.6),
                                      fontSize: 14,
                                    ),
                                  ),
                                  dropdownColor: const Color(0xff16213e),
                                  style: GoogleFonts.mulish(
                                    color: Colors.white,
                                    fontSize: 14,
                                  ),
                                  isExpanded: true,
                                  items: [
                                    DropdownMenuItem<String>(
                                      value: null,
                                      child: Text(
                                        'Tất cả ngày',
                                        style: GoogleFonts.mulish(
                                            color: Colors.white),
                                      ),
                                    ),
                                    ..._getAvailableDates().map((date) {
                                      return DropdownMenuItem<String>(
                                        value: date,
                                        child: Text(
                                          date,
                                          style: GoogleFonts.mulish(
                                              color: Colors.white),
                                        ),
                                      );
                                    }),
                                  ],
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedDate = value;
                                    });
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 16),

                      // Movie Filter
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Phim:',
                              style: GoogleFonts.mulish(
                                color: Colors.white.withOpacity(0.8),
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Container(
                              width: double.infinity,
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 12),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.2),
                                ),
                              ),
                              child: DropdownButtonHideUnderline(
                                child: DropdownButton<int>(
                                  value: _selectedMovieId,
                                  hint: Text(
                                    'Chọn phim',
                                    style: GoogleFonts.mulish(
                                      color: Colors.white.withOpacity(0.6),
                                      fontSize: 14,
                                    ),
                                  ),
                                  dropdownColor: const Color(0xff16213e),
                                  style: GoogleFonts.mulish(
                                    color: Colors.white,
                                    fontSize: 14,
                                  ),
                                  isExpanded: true,
                                  items: [
                                    DropdownMenuItem<int>(
                                      value: null,
                                      child: Text(
                                        'Tất cả phim',
                                        style: GoogleFonts.mulish(
                                            color: Colors.white),
                                      ),
                                    ),
                                    ..._getAvailableMovies().map((movie) {
                                      return DropdownMenuItem<int>(
                                        value: movie.id,
                                        child: Text(
                                          movie.title,
                                          style: GoogleFonts.mulish(
                                              color: Colors.white),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      );
                                    }),
                                  ],
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedMovieId = value;
                                    });
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                ],
              ],
            ),
          ),

          // Active Filter Chips
          if (_hasActiveFilters)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Bộ lọc đang áp dụng:',
                    style: GoogleFonts.mulish(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      if (_searchQuery.isNotEmpty)
                        _buildFilterChip(
                          'Từ khóa: "$_searchQuery"',
                          () {
                            setState(() {
                              _searchQuery = '';
                            });
                            _searchController.clear();
                          },
                        ),
                      if (_selectedDate != null)
                        _buildFilterChip(
                          'Ngày: $_selectedDate',
                          () {
                            setState(() {
                              _selectedDate = null;
                            });
                          },
                        ),
                      if (_selectedMovieId != null)
                        _buildFilterChip(
                          'Phim: "${_scheduleController.getMovieById(_selectedMovieId!)?.title ?? 'Không xác định'}"',
                          () {
                            setState(() {
                              _selectedMovieId = null;
                            });
                          },
                        ),
                    ],
                  ),
                ],
              ),
            ),

          const SizedBox(height: 16),

          // Schedule list
          Expanded(
            child: Obx(() {
              // Check if any component is still loading
              final isAnyLoading =
                  _scheduleController.isLoadingTheaters.value ||
                      _scheduleController.isLoadingMovies.value ||
                      _scheduleController.isLoadingScreens.value ||
                      _scheduleController.isLoadingShowtimes.value;

              if (isAnyLoading) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const CircularProgressIndicator(color: Colors.white),
                      const SizedBox(height: 16),
                      Text(
                        _getLoadingMessage(),
                        style: GoogleFonts.mulish(
                          color: Colors.white.withOpacity(0.7),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                );
              }

              final filteredShowtimes = _getFilteredShowtimes();

              if (_scheduleController.showtimes.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.schedule,
                        size: 64,
                        color: Colors.white.withOpacity(0.5),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Chưa có lịch chiếu nào',
                        style: GoogleFonts.mulish(
                          color: Colors.white.withOpacity(0.7),
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton.icon(
                        onPressed: () => _navigateToCreateSchedule(),
                        icon: const Icon(Icons.add),
                        label: Text(
                          'Tạo lịch chiếu đầu tiên',
                          style: GoogleFonts.mulish(),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                );
              }

              if (filteredShowtimes.isEmpty && _hasActiveFilters) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.search_off,
                        size: 64,
                        color: Colors.white.withOpacity(0.5),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Không tìm thấy lịch chiếu nào',
                        style: GoogleFonts.mulish(
                          color: Colors.white.withOpacity(0.7),
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _buildFilterSummary(),
                        style: GoogleFonts.mulish(
                          color: Colors.white.withOpacity(0.6),
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _clearAllFilters,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white.withOpacity(0.1),
                          foregroundColor: Colors.white,
                        ),
                        child: Text(
                          'Xóa tất cả bộ lọc',
                          style: GoogleFonts.mulish(),
                        ),
                      ),
                    ],
                  ),
                );
              }

              return ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: filteredShowtimes.length,
                itemBuilder: (context, index) {
                  final showtime = filteredShowtimes[index];
                  return _buildShowtimeCard(showtime);
                },
              );
            }),
          ),
        ],
      ),
    );
  }

  // Navigation methods
  void _navigateToCreateSchedule() {
    Get.to(() => const ScheduleFormPage());
  }

  void _navigateToEditSchedule(ShowtimeModel showtime) {
    Get.to(() => ScheduleFormPage(showtime: showtime));
  }

  // Build showtime card
  Widget _buildShowtimeCard(ShowtimeModel showtime) {
    final movie = _scheduleController.getMovieById(showtime.movieId);
    final theater = _scheduleController.getTheaterById(showtime.theaterId);
    final screen = _scheduleController.getScreenById(showtime.screenId);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with movie title and status
            Row(
              children: [
                Expanded(
                  child: Text(
                    movie?.title ?? 'Phim không tìm thấy',
                    style: GoogleFonts.mulish(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
                _buildStatusChip(showtime.status),
              ],
            ),
            const SizedBox(height: 8),

            // Theater and screen info
            Row(
              children: [
                Icon(
                  Icons.local_movies,
                  color: Colors.white.withOpacity(0.7),
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '${theater?.name ?? 'Rạp không tìm thấy'} • ${screen?.name ?? 'Phòng không tìm thấy'}',
                    style: GoogleFonts.mulish(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Date and time info
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  color: Colors.white.withOpacity(0.7),
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  '${showtime.date} • ${showtime.time} - ${showtime.endTime}',
                  style: GoogleFonts.mulish(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Seats info
            Row(
              children: [
                Icon(
                  Icons.event_seat,
                  color: Colors.white.withOpacity(0.7),
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  'Còn ${showtime.availableSeats} ghế trống',
                  style: GoogleFonts.mulish(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () => _navigateToEditSchedule(showtime),
                  icon: const Icon(Icons.edit, size: 16),
                  label: Text(
                    'Sửa',
                    style: GoogleFonts.mulish(fontWeight: FontWeight.w500),
                  ),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.blue,
                  ),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _showDeleteConfirmation(showtime),
                  icon: const Icon(Icons.delete, size: 16),
                  label: Text(
                    'Xóa',
                    style: GoogleFonts.mulish(fontWeight: FontWeight.w500),
                  ),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(ShowtimeStatus status) {
    Color color;
    String text;

    switch (status) {
      case ShowtimeStatus.active:
        color = Colors.green;
        text = 'Hoạt động';
        break;
      case ShowtimeStatus.cancelled:
        color = Colors.red;
        text = 'Đã hủy';
        break;
      case ShowtimeStatus.full:
        color = Colors.orange;
        text = 'Hết chỗ';
        break;
      case ShowtimeStatus.ended:
        color = Colors.grey;
        text = 'Đã kết thúc';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.5)),
      ),
      child: Text(
        text,
        style: GoogleFonts.mulish(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  void _showDeleteConfirmation(ShowtimeModel showtime) {
    Get.dialog(
      AlertDialog(
        backgroundColor: const Color(0xff16213e),
        title: Text(
          'Xác nhận xóa',
          style: GoogleFonts.mulish(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          'Bạn có chắc chắn muốn xóa lịch chiếu này không?',
          style: GoogleFonts.mulish(color: Colors.white),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              'Hủy',
              style: GoogleFonts.mulish(color: Colors.grey),
            ),
          ),
          TextButton(
            onPressed: () async {
              Get.back();
              final success =
                  await _scheduleController.deleteShowtime(showtime.id);
              if (success) {
                Get.snackbar(
                  'Thành công',
                  'Đã xóa lịch chiếu',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              }
            },
            child: Text(
              'Xóa',
              style: GoogleFonts.mulish(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
