import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import '../models/payment_model.dart';
import 'paypal_service.dart';

class PaymentService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _collection = 'payments';

  // Get all payments
  Future<List<PaymentModel>> getAllPayments() async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => PaymentModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get payments: $e');
    }
  }

  // Get payments by user
  Future<List<PaymentModel>> getPaymentsByUser(String userId) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => PaymentModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get payments by user: $e');
    }
  }

  // Get payment by ID
  Future<PaymentModel?> getPaymentById(String paymentId) async {
    try {
      final doc = await _firestore.collection(_collection).doc(paymentId).get();

      if (doc.exists) {
        return PaymentModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get payment: $e');
    }
  }

  // Get payment by ticket ID
  Future<PaymentModel?> getPaymentByTicketId(String ticketId) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('ticketId', isEqualTo: ticketId)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        return PaymentModel.fromFirestore(snapshot.docs.first);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get payment by ticket: $e');
    }
  }

  // Get payments by status
  Future<List<PaymentModel>> getPaymentsByStatus(PaymentStatus status) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('status', isEqualTo: status.name)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => PaymentModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get payments by status: $e');
    }
  }

  // Get payments by method
  Future<List<PaymentModel>> getPaymentsByMethod(PaymentMethod method) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('method', isEqualTo: method.name)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => PaymentModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get payments by method: $e');
    }
  }

  // Create payment
  Future<PaymentModel> createPayment(PaymentModel payment) async {
    try {
      final now = DateTime.now();
      final paymentData = payment.copyWith(createdAt: now);

      final docRef = await _firestore
          .collection(_collection)
          .add(paymentData.toFirestore());

      return paymentData.copyWith(id: docRef.id);
    } catch (e) {
      throw Exception('Failed to create payment: $e');
    }
  }

  // Update payment status
  Future<void> updatePaymentStatus(String paymentId, PaymentStatus status,
      {String? transactionId, Map<String, dynamic>? gatewayResponse}) async {
    try {
      final updateData = <String, dynamic>{
        'status': status.name,
      };

      if (status == PaymentStatus.completed) {
        updateData['completedAt'] = Timestamp.now();
      } else if (status == PaymentStatus.refunded) {
        updateData['refundedAt'] = Timestamp.now();
      }

      if (transactionId != null) {
        updateData['transactionId'] = transactionId;
      }

      if (gatewayResponse != null) {
        updateData['gatewayResponse'] = gatewayResponse;
      }

      await _firestore
          .collection(_collection)
          .doc(paymentId)
          .update(updateData);
    } catch (e) {
      throw Exception('Failed to update payment status: $e');
    }
  }

  // Process payment (with PayPal integration)
  Future<Map<String, dynamic>> processPayment(PaymentModel payment,
      {BuildContext? context}) async {
    try {
      // Handle PayPal payments
      if (payment.method == PaymentMethod.paypal) {
        if (context == null) {
          throw Exception('Context is required for PayPal payments');
        }

        return await _processPayPalPayment(payment, context);
      }

      // Handle other payment methods (simulate)
      return await _processOtherPayments(payment);
    } catch (e) {
      await updatePaymentStatus(
        payment.id,
        PaymentStatus.failed,
        gatewayResponse: {
          'success': false,
          'error': e.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      throw Exception('Failed to process payment: $e');
    }
  }

  // Process PayPal payment
  Future<Map<String, dynamic>> _processPayPalPayment(
      PaymentModel payment, BuildContext context) async {
    try {
      // Convert VND to USD for PayPal
      final usdAmount = PayPalService.convertVndToUsd(payment.amount);

      // Create payment description
      final description = PayPalService.createPaymentDescription(
        movieTitle: 'Movie Ticket',
        theaterName: 'Cinema',
        showtime: DateTime.now().toString(),
        seats: ['Selected Seats'],
      );

      // Process PayPal payment
      final paypalResult = await PayPalService.processPayment(
        amount: usdAmount,
        currency: 'USD',
        description: description,
        context: context,
      );

      if (paypalResult['success'] == true) {
        await updatePaymentStatus(
          payment.id,
          PaymentStatus.completed,
          transactionId: paypalResult['paymentId'],
          gatewayResponse: paypalResult,
        );

        return {
          'success': true,
          'transactionId': paypalResult['paymentId'],
          'message': 'PayPal payment completed successfully',
          'paypalData': paypalResult,
        };
      } else {
        await updatePaymentStatus(
          payment.id,
          PaymentStatus.failed,
          gatewayResponse: paypalResult,
        );

        return {
          'success': false,
          'error': paypalResult['error'] ?? 'PayPal payment failed',
          'message': 'PayPal payment failed. Please try again.',
        };
      }
    } catch (e) {
      throw Exception('PayPal payment error: $e');
    }
  }

  // Process other payment methods (simulate)
  Future<Map<String, dynamic>> _processOtherPayments(
      PaymentModel payment) async {
    // Simulate payment processing
    await Future.delayed(const Duration(seconds: 2));

    // Simulate success/failure (90% success rate)
    final isSuccess = DateTime.now().millisecond % 10 != 0;

    if (isSuccess) {
      final transactionId = 'TXN_${DateTime.now().millisecondsSinceEpoch}';

      await updatePaymentStatus(
        payment.id,
        PaymentStatus.completed,
        transactionId: transactionId,
        gatewayResponse: {
          'success': true,
          'transactionId': transactionId,
          'timestamp': DateTime.now().toIso8601String(),
          'gateway': payment.provider,
        },
      );

      return {
        'success': true,
        'transactionId': transactionId,
        'message': 'Payment processed successfully',
      };
    } else {
      await updatePaymentStatus(
        payment.id,
        PaymentStatus.failed,
        gatewayResponse: {
          'success': false,
          'error': 'Payment declined by bank',
          'timestamp': DateTime.now().toIso8601String(),
          'gateway': payment.provider,
        },
      );

      return {
        'success': false,
        'error': 'Payment declined by bank',
        'message': 'Payment failed. Please try again.',
      };
    }
  }

  // Refund payment
  Future<void> refundPayment(String paymentId, {String? reason}) async {
    try {
      final payment = await getPaymentById(paymentId);
      if (payment == null) throw Exception('Payment not found');

      if (!payment.canBeRefunded) {
        throw Exception('Payment cannot be refunded');
      }

      await updatePaymentStatus(
        paymentId,
        PaymentStatus.refunded,
        gatewayResponse: {
          'refunded': true,
          'reason': reason ?? 'Refund requested',
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      throw Exception('Failed to refund payment: $e');
    }
  }

  // Stream for real-time updates
  Stream<List<PaymentModel>> getPaymentsStream() {
    return _firestore
        .collection(_collection)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => PaymentModel.fromFirestore(doc))
            .toList());
  }

  Stream<List<PaymentModel>> getPaymentsByUserStream(String userId) {
    return _firestore
        .collection(_collection)
        .where('userId', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => PaymentModel.fromFirestore(doc))
            .toList());
  }

  Stream<PaymentModel?> getPaymentStream(String paymentId) {
    return _firestore
        .collection(_collection)
        .doc(paymentId)
        .snapshots()
        .map((doc) => doc.exists ? PaymentModel.fromFirestore(doc) : null);
  }

  // Statistics
  Future<Map<String, dynamic>> getPaymentStatistics() async {
    try {
      final snapshot = await _firestore.collection(_collection).get();

      int totalPayments = 0;
      int completedPayments = 0;
      int failedPayments = 0;
      int refundedPayments = 0;
      double totalRevenue = 0.0;
      double refundedAmount = 0.0;
      final methodCounts = <String, int>{};
      final providerCounts = <String, int>{};

      for (final doc in snapshot.docs) {
        final payment = PaymentModel.fromFirestore(doc);
        totalPayments++;

        switch (payment.status) {
          case PaymentStatus.completed:
            completedPayments++;
            totalRevenue += payment.amount;
            break;
          case PaymentStatus.failed:
            failedPayments++;
            break;
          case PaymentStatus.refunded:
            refundedPayments++;
            refundedAmount += payment.amount;
            break;
          case PaymentStatus.pending:
            break;
        }

        // Count by method
        final method = payment.method.displayName;
        methodCounts[method] = (methodCounts[method] ?? 0) + 1;

        // Count by provider
        providerCounts[payment.provider] =
            (providerCounts[payment.provider] ?? 0) + 1;
      }

      return {
        'totalPayments': totalPayments,
        'completedPayments': completedPayments,
        'failedPayments': failedPayments,
        'refundedPayments': refundedPayments,
        'pendingPayments': totalPayments -
            completedPayments -
            failedPayments -
            refundedPayments,
        'totalRevenue': totalRevenue,
        'refundedAmount': refundedAmount,
        'netRevenue': totalRevenue - refundedAmount,
        'successRate':
            totalPayments > 0 ? (completedPayments / totalPayments) * 100 : 0,
        'methodCounts': methodCounts,
        'providerCounts': providerCounts,
      };
    } catch (e) {
      throw Exception('Failed to get payment statistics: $e');
    }
  }

  // Get revenue by date range
  Future<Map<String, double>> getRevenueByDateRange(
      DateTime startDate, DateTime endDate) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('status', isEqualTo: 'completed')
          .where('completedAt',
              isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('completedAt',
              isLessThanOrEqualTo: Timestamp.fromDate(endDate))
          .get();

      final dailyRevenue = <String, double>{};

      for (final doc in snapshot.docs) {
        final payment = PaymentModel.fromFirestore(doc);
        if (payment.completedAt != null) {
          final date = payment.completedAt!;
          final dateKey =
              '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
          dailyRevenue[dateKey] =
              (dailyRevenue[dateKey] ?? 0.0) + payment.amount;
        }
      }

      return dailyRevenue;
    } catch (e) {
      throw Exception('Failed to get revenue by date range: $e');
    }
  }
}
