const {onSchedule} = require('firebase-functions/v2/scheduler');
const {onCall, HttpsError} = require('firebase-functions/v2/https');
const {initializeApp} = require('firebase-admin/app');
const {getFirestore, FieldValue} = require('firebase-admin/firestore');
const {getAuth} = require('firebase-admin/auth');

// Initialize Firebase Admin SDK
initializeApp();

// Cloud Function to automatically update expired tickets
// Runs every hour
exports.updateExpiredTickets = onSchedule('0 * * * *', async (event) => {
    console.log('Starting expired tickets update...');

    try {
      const firestore = getFirestore();
      const now = new Date();

      // Format current date and time for comparison
      const currentDate = now.toISOString().split('T')[0]; // YYYY-MM-DD
      const currentTime = now.toTimeString().split(' ')[0].substring(0, 5); // HH:MM

      console.log(`Current date: ${currentDate}, Current time: ${currentTime}`);

      // Query for tickets that should be expired
      // 1. Tickets with date before today
      const pastDateQuery = firestore
        .collection('tickets')
        .where('status', '==', 'confirmed')
        .where('date', '<', currentDate);

      // 2. Tickets for today but with time that has passed
      const todayQuery = firestore
        .collection('tickets')
        .where('status', '==', 'confirmed')
        .where('date', '==', currentDate)
        .where('time', '<', currentTime);

      const [pastDateSnapshot, todaySnapshot] = await Promise.all([
        pastDateQuery.get(),
        todayQuery.get()
      ]);

      const expiredTickets = [...pastDateSnapshot.docs, ...todaySnapshot.docs];

      console.log(`Found ${expiredTickets.length} tickets to expire`);

      // Update tickets in batches
      const batch = firestore.batch();
      let batchCount = 0;
      const maxBatchSize = 500; // Firestore batch limit

      for (const doc of expiredTickets) {
        if (batchCount >= maxBatchSize) {
          // Commit current batch and start a new one
          await batch.commit();
          batchCount = 0;
        }

        batch.update(doc.ref, {
          status: 'expired',
          updatedAt: FieldValue.serverTimestamp()
        });

        batchCount++;
        console.log(`Marked ticket ${doc.id} as expired`);
      }

      // Commit the final batch
      if (batchCount > 0) {
        await batch.commit();
      }

      console.log(`Successfully updated ${expiredTickets.length} expired tickets`);

      return {
        success: true,
        updatedCount: expiredTickets.length,
        timestamp: now.toISOString()
      };

    } catch (error) {
      console.error('Error updating expired tickets:', error);
      throw new Error('Failed to update expired tickets');
    }
  });

// HTTP function to manually trigger expired tickets update
exports.manualUpdateExpiredTickets = onCall(async (request) => {
  // Check if user is authenticated and has admin privileges
  if (!request.auth) {
    throw new HttpsError('unauthenticated', 'User must be authenticated');
  }

  try {
    // Get user's custom claims to check if they're admin
    const auth = getAuth();
    const userRecord = await auth.getUser(request.auth.uid);
    const customClaims = userRecord.customClaims || {};

    if (!customClaims.admin && !customClaims.developer) {
      throw new HttpsError('permission-denied', 'User must be admin or developer');
    }

    console.log(`Manual expired tickets update triggered by ${request.auth.uid}`);

    const firestore = getFirestore();
    const now = new Date();

    // Format current date and time for comparison
    const currentDate = now.toISOString().split('T')[0]; // YYYY-MM-DD
    const currentTime = now.toTimeString().split(' ')[0].substring(0, 5); // HH:MM

    console.log(`Current date: ${currentDate}, Current time: ${currentTime}`);

    // Query for tickets that should be expired
    const pastDateQuery = firestore
      .collection('tickets')
      .where('status', '==', 'confirmed')
      .where('date', '<', currentDate);

    const todayQuery = firestore
      .collection('tickets')
      .where('status', '==', 'confirmed')
      .where('date', '==', currentDate)
      .where('time', '<', currentTime);

    const [pastDateSnapshot, todaySnapshot] = await Promise.all([
      pastDateQuery.get(),
      todayQuery.get()
    ]);

    const expiredTickets = [...pastDateSnapshot.docs, ...todaySnapshot.docs];

    console.log(`Found ${expiredTickets.length} tickets to expire`);

    // Update tickets in batches
    const batch = firestore.batch();
    let batchCount = 0;
    const maxBatchSize = 500;

    for (const doc of expiredTickets) {
      if (batchCount >= maxBatchSize) {
        await batch.commit();
        batchCount = 0;
      }

      batch.update(doc.ref, {
        status: 'expired',
        updatedAt: FieldValue.serverTimestamp()
      });

      batchCount++;
    }

    if (batchCount > 0) {
      await batch.commit();
    }

    console.log(`Successfully updated ${expiredTickets.length} expired tickets`);

    return {
      success: true,
      updatedCount: expiredTickets.length,
      timestamp: now.toISOString()
    };

  } catch (error) {
    console.error('Error in manual update:', error);
    throw new HttpsError('internal', 'Failed to update expired tickets');
  }
});

// Function to get expired tickets statistics
exports.getExpiredTicketsStats = onCall(async (request) => {
  if (!request.auth) {
    throw new HttpsError('unauthenticated', 'User must be authenticated');
  }

  try {
    const auth = getAuth();
    const userRecord = await auth.getUser(request.auth.uid);
    const customClaims = userRecord.customClaims || {};

    if (!customClaims.admin && !customClaims.developer) {
      throw new HttpsError('permission-denied', 'User must be admin or developer');
    }

    const firestore = getFirestore();
    const now = new Date();
    const currentDate = now.toISOString().split('T')[0];
    const currentTime = now.toTimeString().split(' ')[0].substring(0, 5);

    // Get various ticket counts
    const [confirmedSnapshot, expiredSnapshot, shouldBeExpiredSnapshot] = await Promise.all([
      firestore.collection('tickets').where('status', '==', 'confirmed').get(),
      firestore.collection('tickets').where('status', '==', 'expired').get(),
      firestore.collection('tickets')
        .where('status', '==', 'confirmed')
        .where('date', '<', currentDate)
        .get()
    ]);

    return {
      totalConfirmed: confirmedSnapshot.size,
      totalExpired: expiredSnapshot.size,
      shouldBeExpired: shouldBeExpiredSnapshot.size,
      lastCheck: now.toISOString()
    };

  } catch (error) {
    console.error('Error getting stats:', error);
    throw new HttpsError('internal', 'Failed to get statistics');
  }
});
