import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../config/paypal_config.dart';
import '../widgets/paypal_payment_wrapper.dart';

class PayPalService {
  // Process PayPal payment
  static Future<Map<String, dynamic>> processPayment({
    required double amount,
    required String currency,
    required String description,
    required BuildContext context,
  }) async {
    try {
      // Check if PayPal is configured
      if (!PayPalConfig.isConfigured) {
        return {
          'success': false,
          'error': 'PayPal is not configured. Please check your credentials.',
          'method': 'paypal',
        };
      }

      // Create PayPal transactions
      final transactions = [
        {
          "amount": {
            "total": amount.toStringAsFixed(2),
            "currency": currency,
            "details": {
              "subtotal": amount.toStringAsFixed(2),
              "shipping": '0',
              "shipping_discount": 0
            }
          },
          "description": description,
          "item_list": {
            "items": [
              {
                "name": "Movie Tickets",
                "quantity": 1,
                "price": amount.toStringAsFixed(2),
                "currency": currency
              }
            ],
          }
        }
      ];

      // Navigate to PayPal checkout using wrapper
      final result = await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (BuildContext context) => PayPalPaymentWrapper(
            sandboxMode: PayPalConfig.useSandbox,
            clientId: PayPalConfig.clientId,
            secretKey: PayPalConfig.secretKey,
            transactions: transactions,
            note: "Movie ticket payment - Đớp Phim App",
          ),
        ),
      );

      if (result != null && result is Map) {
        print("PayPal result received: $result");

        // Check if payment was cancelled
        if (result.containsKey('cancelled')) {
          return {
            'success': false,
            'error': 'Payment was cancelled by user',
            'method': 'paypal',
          };
        }

        // Check if there was an error in the result
        if (result.containsKey('error') && result['error'] != false) {
          return {
            'success': false,
            'error': result['error'].toString(),
            'method': 'paypal',
          };
        }

        // Check for PayPal success indicators
        bool isSuccess = false;
        String? paymentId;
        String? payerId;

        // PayPal success can be indicated by:
        // 1. error: false AND message: Success
        // 2. data object with payment details
        if (result['error'] == false && result['message'] == 'Success') {
          isSuccess = true;

          // Extract payment details from data object
          if (result['data'] != null && result['data'] is Map) {
            final data = result['data'] as Map;
            paymentId = data['id']?.toString();

            // Extract payer ID from payer info
            if (data['payer'] != null && data['payer']['payer_info'] != null) {
              payerId = data['payer']['payer_info']['payer_id']?.toString();
            }
          }
        }

        if (isSuccess) {
          print("PayPal payment confirmed successful");
          return {
            'success': true,
            'paymentId':
                paymentId ?? 'PAYPAL_${DateTime.now().millisecondsSinceEpoch}',
            'payerId': payerId ?? 'UNKNOWN_PAYER',
            'token': result['token']?.toString() ?? 'NO_TOKEN',
            'amount': amount,
            'currency': currency,
            'method': 'paypal',
            'timestamp': DateTime.now().toIso8601String(),
            'description': description,
            'paypalData': result,
          };
        } else {
          print("PayPal payment not confirmed as successful");
          return {
            'success': false,
            'error': 'PayPal payment verification failed',
            'method': 'paypal',
            'paypalData': result,
          };
        }
      } else {
        return {
          'success': false,
          'error': 'Payment was cancelled or failed',
          'method': 'paypal',
        };
      }
    } catch (e) {
      print('PayPal payment error: $e');
      return {
        'success': false,
        'error': e.toString(),
        'method': 'paypal',
      };
    }
  }

  // Validate PayPal configuration
  static bool isConfigured() {
    return PayPalConfig.isConfigured;
  }

  // Get PayPal environment info
  static Map<String, dynamic> getEnvironmentInfo() {
    return {
      'environment': PayPalConfig.environmentName,
      'clientId': PayPalConfig.isConfigured
          ? '${PayPalConfig.clientId.substring(0, 10)}...'
          : 'Not configured',
      'configured': PayPalConfig.isConfigured,
    };
  }

  // Convert VND to USD for PayPal (PayPal doesn't support VND)
  static double convertVndToUsd(double vndAmount) {
    // Exchange rate VND to USD (approximate, should be updated from real API)
    const double exchangeRate = 24000.0; // 1 USD = 24,000 VND (approximate)
    return vndAmount / exchangeRate;
  }

  // Format amount for display
  static String formatAmount(double amount, String currency) {
    if (currency == 'USD') {
      return '\$${amount.toStringAsFixed(2)}';
    } else if (currency == 'VND') {
      return '${amount.toStringAsFixed(0).replaceAllMapped(
            RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
            (Match m) => '${m[1]},',
          )} ₫';
    }
    return '${amount.toStringAsFixed(2)} $currency';
  }

  // Create payment description
  static String createPaymentDescription({
    required String movieTitle,
    required String theaterName,
    required String showtime,
    required List<String> seats,
  }) {
    return 'Movie: $movieTitle | Theater: $theaterName | Time: $showtime | Seats: ${seats.join(", ")}';
  }

  // Simulate payment for testing (when PayPal is not configured)
  static Future<Map<String, dynamic>> simulatePayment({
    required double amount,
    required String currency,
    required String description,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 2));

    return {
      'success': true,
      'paymentId': 'SIMULATED_${DateTime.now().millisecondsSinceEpoch}',
      'payerId': 'SIMULATED_PAYER',
      'token': 'SIMULATED_TOKEN',
      'amount': amount,
      'currency': currency,
      'method': 'paypal_simulation',
      'timestamp': DateTime.now().toIso8601String(),
      'description': description,
      'note': 'This is a simulated payment for testing purposes',
    };
  }


  // Show PayPal configuration dialog for developers
  static void showConfigurationDialog(BuildContext context) {
    final envInfo = getEnvironmentInfo();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('PayPal Configuration'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Environment: ${envInfo['environment']}'),
            Text('Client ID: ${envInfo['clientId']}'),
            Text('Configured: ${envInfo['configured']}'),
            const SizedBox(height: 16),
            if (!envInfo['configured'])
              const Text(
                'PayPal is not properly configured. Please update the client ID and secret key in PayPalService.',
                style: TextStyle(color: Colors.red),
              ),
            if (envInfo['configured'])
              const Text(
                'PayPal is configured and ready to use.',
                style: TextStyle(color: Colors.green),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
