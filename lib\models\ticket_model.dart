import 'package:cloud_firestore/cloud_firestore.dart';

enum TicketStatus { confirmed, cancelled, used, expired }

extension TicketStatusExtension on TicketStatus {
  String get name {
    switch (this) {
      case TicketStatus.confirmed:
        return 'confirmed';
      case TicketStatus.cancelled:
        return 'cancelled';
      case TicketStatus.used:
        return 'used';
      case TicketStatus.expired:
        return 'expired';
    }
  }

  static TicketStatus fromString(String? value) {
    switch (value) {
      case 'cancelled':
        return TicketStatus.cancelled;
      case 'used':
        return TicketStatus.used;
      case 'expired':
        return TicketStatus.expired;
      default:
        return TicketStatus.confirmed;
    }
  }

  String get displayName {
    switch (this) {
      case TicketStatus.confirmed:
        return 'Đã <PERSON>';
      case TicketStatus.cancelled:
        return 'Đã Hủy';
      case TicketStatus.used:
        return 'Đã Sử Dụng';
      case TicketStatus.expired:
        return 'Đã <PERSON>ết <PERSON>';
    }
  }
}

class TicketSeat {
  final String row;
  final String number;
  final String type;
  final double price;

  TicketSeat({
    required this.row,
    required this.number,
    required this.type,
    required this.price,
  });

  factory TicketSeat.fromJson(Map<String, dynamic> json) {
    return TicketSeat(
      row: json['row'] ?? '',
      number: json['number'] ?? '',
      type: json['type'] ?? 'standard',
      price: json['price']?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'row': row,
      'number': number,
      'type': type,
      'price': price,
    };
  }

  String get seatId => '$row$number';
  String get displayName => '$row$number';

  TicketSeat copyWith({
    String? row,
    String? number,
    String? type,
    double? price,
  }) {
    return TicketSeat(
      row: row ?? this.row,
      number: number ?? this.number,
      type: type ?? this.type,
      price: price ?? this.price,
    );
  }
}

class Ticket {
  final String id;
  final String userId;
  final int movieId;
  final String movieTitle;
  final String? moviePosterPath;
  final String theaterId;
  final String theaterName;
  final String screenId;
  final String screenName;
  final String showtimeId;
  final String date;
  final String time;
  final List<TicketSeat> seats;
  final double totalPrice;
  final double discountApplied;
  final double finalPrice;
  final String paymentMethod; // "credit_card", "debit_card", "e_wallet", "cash"
  final String? paymentId;
  final String bookingCode; // mã đặt vé duy nhất
  final String? qrCode; // URL của QR code
  final TicketStatus status;
  final DateTime purchaseDate;
  final DateTime? usedAt;
  final DateTime? cancelledAt;
  final double? refundAmount;
  final int loyaltyPointsEarned;
  final int loyaltyPointsUsed;

  Ticket({
    required this.id,
    required this.userId,
    required this.movieId,
    required this.movieTitle,
    this.moviePosterPath,
    required this.theaterId,
    required this.theaterName,
    required this.screenId,
    required this.screenName,
    required this.showtimeId,
    required this.date,
    required this.time,
    required this.seats,
    required this.totalPrice,
    this.discountApplied = 0.0,
    required this.finalPrice,
    required this.paymentMethod,
    this.paymentId,
    required this.bookingCode,
    this.qrCode,
    this.status = TicketStatus.confirmed,
    required this.purchaseDate,
    this.usedAt,
    this.cancelledAt,
    this.refundAmount,
    this.loyaltyPointsEarned = 0,
    this.loyaltyPointsUsed = 0,
  });

  String get fullPosterPath {
    if (moviePosterPath == null || moviePosterPath!.isEmpty) {
      return ''; // Return empty string to trigger errorBuilder
    }
    if (moviePosterPath!.startsWith('http')) {
      return moviePosterPath!;
    }
    return 'https://image.tmdb.org/t/p/w200$moviePosterPath';
  }

  factory Ticket.fromJson(Map<String, dynamic> json) {
    // Handle both old and new format
    List<TicketSeat> seatsList = [];
    if (json['seats'] != null) {
      // New format
      seatsList = (json['seats'] as List<dynamic>)
          .map((seat) => TicketSeat.fromJson(seat))
          .toList();
    } else if (json['seat'] != null) {
      // Old format - convert single seat to list
      seatsList = [
        TicketSeat(
          row: json['seat'].toString().substring(0, 1),
          number: json['seat'].toString().substring(1),
          type: 'standard',
          price: json['price']?.toDouble() ?? 0.0,
        )
      ];
    }

    return Ticket(
      id: json['id'] ?? '',
      userId: json['user_id'] ?? json['userId'] ?? '',
      movieId: json['movie_id'] ?? json['movieId'] ?? 0,
      movieTitle: json['movie_title'] ?? json['movieTitle'] ?? '',
      moviePosterPath: json['movie_poster_path'] ?? json['moviePosterPath'],
      theaterId:
          json['theater_id'] ?? json['theaterId'] ?? json['theater'] ?? '',
      theaterName:
          json['theater_name'] ?? json['theaterName'] ?? json['theater'] ?? '',
      screenId: json['screen_id'] ?? json['screenId'] ?? '',
      screenName: json['screen_name'] ?? json['screenName'] ?? '',
      showtimeId: json['showtime_id'] ?? json['showtimeId'] ?? '',
      date: json['date'] ?? '',
      time: json['time'] ?? '',
      seats: seatsList,
      totalPrice: json['total_price']?.toDouble() ??
          json['totalPrice']?.toDouble() ??
          json['price']?.toDouble() ??
          0.0,
      discountApplied: json['discount_applied']?.toDouble() ??
          json['discountApplied']?.toDouble() ??
          0.0,
      finalPrice: json['final_price']?.toDouble() ??
          json['finalPrice']?.toDouble() ??
          json['price']?.toDouble() ??
          0.0,
      paymentMethod: json['payment_method'] ?? json['paymentMethod'] ?? 'cash',
      paymentId: json['payment_id'] ?? json['paymentId'],
      bookingCode: json['booking_code'] ?? json['bookingCode'] ?? '',
      qrCode: json['qr_code'] ?? json['qrCode'],
      status: TicketStatusExtension.fromString(json['status']),
      purchaseDate: json['purchase_date'] != null
          ? (json['purchase_date'] as Timestamp).toDate()
          : json['purchaseDate'] != null
              ? (json['purchaseDate'] as Timestamp).toDate()
              : DateTime.now(),
      usedAt: json['used_at'] != null
          ? (json['used_at'] as Timestamp).toDate()
          : json['usedAt'] != null
              ? (json['usedAt'] as Timestamp).toDate()
              : null,
      cancelledAt: json['cancelled_at'] != null
          ? (json['cancelled_at'] as Timestamp).toDate()
          : json['cancelledAt'] != null
              ? (json['cancelledAt'] as Timestamp).toDate()
              : null,
      refundAmount:
          json['refund_amount']?.toDouble() ?? json['refundAmount']?.toDouble(),
      loyaltyPointsEarned:
          json['loyalty_points_earned'] ?? json['loyaltyPointsEarned'] ?? 0,
      loyaltyPointsUsed:
          json['loyalty_points_used'] ?? json['loyaltyPointsUsed'] ?? 0,
    );
  }

  factory Ticket.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Ticket.fromJson({...data, 'id': doc.id});
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'movieId': movieId,
      'movieTitle': movieTitle,
      'moviePosterPath': moviePosterPath,
      'theaterId': theaterId,
      'theaterName': theaterName,
      'screenId': screenId,
      'screenName': screenName,
      'showtimeId': showtimeId,
      'date': date,
      'time': time,
      'seats': seats.map((seat) => seat.toJson()).toList(),
      'totalPrice': totalPrice,
      'discountApplied': discountApplied,
      'finalPrice': finalPrice,
      'paymentMethod': paymentMethod,
      'paymentId': paymentId,
      'bookingCode': bookingCode,
      'qrCode': qrCode,
      'status': status.name,
      'purchaseDate': Timestamp.fromDate(purchaseDate),
      'usedAt': usedAt != null ? Timestamp.fromDate(usedAt!) : null,
      'cancelledAt':
          cancelledAt != null ? Timestamp.fromDate(cancelledAt!) : null,
      'refundAmount': refundAmount,
      'loyaltyPointsEarned': loyaltyPointsEarned,
      'loyaltyPointsUsed': loyaltyPointsUsed,

      // Legacy fields for backward compatibility
      'id': id,
      'movie_id': movieId,
      'movie_title': movieTitle,
      'movie_poster_path': moviePosterPath,
      'theater': theaterName,
      'seat': seats.isNotEmpty ? seats.first.seatId : '',
      'price': finalPrice,
      'user_id': userId,
      'purchase_date': Timestamp.fromDate(purchaseDate),
    };
  }

  Map<String, dynamic> toFirestore() => toJson();

  Ticket copyWith({
    String? id,
    String? userId,
    int? movieId,
    String? movieTitle,
    String? moviePosterPath,
    String? theaterId,
    String? theaterName,
    String? screenId,
    String? screenName,
    String? showtimeId,
    String? date,
    String? time,
    List<TicketSeat>? seats,
    double? totalPrice,
    double? discountApplied,
    double? finalPrice,
    String? paymentMethod,
    String? paymentId,
    String? bookingCode,
    String? qrCode,
    TicketStatus? status,
    DateTime? purchaseDate,
    DateTime? usedAt,
    DateTime? cancelledAt,
    double? refundAmount,
    int? loyaltyPointsEarned,
    int? loyaltyPointsUsed,
  }) {
    return Ticket(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      movieId: movieId ?? this.movieId,
      movieTitle: movieTitle ?? this.movieTitle,
      moviePosterPath: moviePosterPath ?? this.moviePosterPath,
      theaterId: theaterId ?? this.theaterId,
      theaterName: theaterName ?? this.theaterName,
      screenId: screenId ?? this.screenId,
      screenName: screenName ?? this.screenName,
      showtimeId: showtimeId ?? this.showtimeId,
      date: date ?? this.date,
      time: time ?? this.time,
      seats: seats ?? this.seats,
      totalPrice: totalPrice ?? this.totalPrice,
      discountApplied: discountApplied ?? this.discountApplied,
      finalPrice: finalPrice ?? this.finalPrice,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentId: paymentId ?? this.paymentId,
      bookingCode: bookingCode ?? this.bookingCode,
      qrCode: qrCode ?? this.qrCode,
      status: status ?? this.status,
      purchaseDate: purchaseDate ?? this.purchaseDate,
      usedAt: usedAt ?? this.usedAt,
      cancelledAt: cancelledAt ?? this.cancelledAt,
      refundAmount: refundAmount ?? this.refundAmount,
      loyaltyPointsEarned: loyaltyPointsEarned ?? this.loyaltyPointsEarned,
      loyaltyPointsUsed: loyaltyPointsUsed ?? this.loyaltyPointsUsed,
    );
  }

  // Legacy getters for backward compatibility
  String get theater => theaterName;
  String get seat => seats.isNotEmpty ? seats.first.seatId : '';
  double get price => finalPrice;

  // Helper methods
  bool get isConfirmed => status == TicketStatus.confirmed;
  bool get isCancelled => status == TicketStatus.cancelled;
  bool get isUsed => status == TicketStatus.used;
  bool get isExpired => status == TicketStatus.expired;

  int get totalSeats => seats.length;
  String get seatsDisplay => seats.map((s) => s.displayName).join(', ');

  bool get canBeCancelled =>
      status == TicketStatus.confirmed &&
      DateTime.now().isBefore(showDateTime.subtract(const Duration(hours: 2)));

  DateTime get showDateTime {
    final dateParts = date.split('-');
    final timeParts = time.split(':');
    return DateTime(
      int.parse(dateParts[0]),
      int.parse(dateParts[1]),
      int.parse(dateParts[2]),
      int.parse(timeParts[0]),
      int.parse(timeParts[1]),
    );
  }

  String get displayDateTime => '$date $time';
  String get displayStatus => status.displayName;
}

// Model for movie ticket statistics
class MovieTicketStats {
  final int movieId;
  final String movieTitle;
  final String? moviePosterPath;
  final int totalAvailableTickets;
  final int soldTickets;
  final int remainingTickets;
  final double totalRevenue;
  final List<Ticket> tickets;

  MovieTicketStats({
    required this.movieId,
    required this.movieTitle,
    this.moviePosterPath,
    required this.totalAvailableTickets,
    required this.soldTickets,
    required this.remainingTickets,
    required this.totalRevenue,
    required this.tickets,
  });

  factory MovieTicketStats.fromData({
    required int movieId,
    required String movieTitle,
    String? moviePosterPath,
    required int totalAvailableTickets,
    required List<Ticket> tickets,
  }) {
    final soldTickets =
        tickets.where((t) => t.status != TicketStatus.cancelled).length;
    final remainingTickets = totalAvailableTickets - soldTickets;
    final totalRevenue = tickets
        .where((t) => t.status != TicketStatus.cancelled)
        .fold<double>(0.0, (total, ticket) => total + ticket.finalPrice);

    return MovieTicketStats(
      movieId: movieId,
      movieTitle: movieTitle,
      moviePosterPath: moviePosterPath,
      totalAvailableTickets: totalAvailableTickets,
      soldTickets: soldTickets,
      remainingTickets: remainingTickets,
      totalRevenue: totalRevenue,
      tickets: tickets,
    );
  }

  String get fullPosterPath {
    if (moviePosterPath == null || moviePosterPath!.isEmpty) {
      return ''; // Return empty string to trigger errorBuilder
    }
    if (moviePosterPath!.startsWith('http')) {
      return moviePosterPath!;
    }
    return 'https://image.tmdb.org/t/p/w200$moviePosterPath';
  }

  double get occupancyRate => totalAvailableTickets > 0
      ? (soldTickets / totalAvailableTickets) * 100
      : 0.0;
}
