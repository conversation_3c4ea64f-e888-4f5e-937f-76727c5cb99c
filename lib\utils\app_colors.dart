import 'package:flutter/material.dart';

/// Centralized color management for the Đớp Phim app
/// Ensures consistent color usage across all screens
class AppColors {
  // Private constructor to prevent instantiation
  AppColors._();

  // Primary gradient colors
  static const Color primaryGradientStart = Color(0xff2B5876);
  static const Color primaryGradientEnd = Color(0xff4E4376);
  
  // Background colors
  static const Color scaffoldBackground = Color(0xff121212);
  static const Color cardBackground = Color(0xff1E1E1E);
  static const Color surfaceColor = Color(0xff2A2A2A);
  
  // Primary colors
  static const Color primaryBlue = Color(0xff2196F3);
  static const Color primaryAmber = Color(0xffFFC107);
  static const Color primaryWhite = Colors.white;
  
  // Accent colors
  static const Color accentBlue = Color(0xff4B79A1);
  static const Color accentTeal = Color(0xff283E51);
  static const Color accentOrange = Color(0xffFF9800);
  
  // Status colors
  static const Color successGreen = Color(0xff4CAF50);
  static const Color warningOrange = Color(0xffFF9800);
  static const Color errorRed = Color(0xffF44336);
  static const Color infoBlue = Color(0xff2196F3);
  
  // Text colors
  static const Color textPrimary = Colors.white;
  static const Color textSecondary = Color(0xffB0B0B0);
  static const Color textTertiary = Color(0xff808080);
  static const Color textDisabled = Color(0xff606060);
  
  // Button colors
  static const Color buttonPrimary = Color(0xff2196F3);
  static const Color buttonSecondary = Color(0xff4B79A1);
  static const Color buttonDanger = Color(0xffF44336);
  static const Color buttonSuccess = Color(0xff4CAF50);
  static const Color buttonWarning = Color(0xffFF9800);
  
  // Border colors
  static const Color borderPrimary = Color(0xff404040);
  static const Color borderSecondary = Color(0xff606060);
  static const Color borderAccent = Color(0xff2196F3);
  
  // Overlay colors
  static const Color overlayLight = Color(0x1AFFFFFF);
  static const Color overlayMedium = Color(0x33FFFFFF);
  static const Color overlayDark = Color(0x66000000);
  
  // Chat/Message colors (for bug reports and notifications)
  static const Color chatSystemBackground = Color(0xff4B79A1);
  static const Color chatDeveloperBackground = Color(0xff2196F3);
  static const Color chatAdminBackground = Color(0xff4CAF50);
  static const Color chatUserBackground = Color(0xff283E51);
  static const Color chatCurrentUserBackground = Color(0xff4B79A1);
  
  // Screen type colors
  static const Color screenStandard = Color(0xff2196F3);
  static const Color screenVip = Color(0xffFFC107);
  static const Color screenImax = Color(0xffF44336);
  static const Color screenDolby = Color(0xff4CAF50);
  static const Color screenPremium = Color(0xff9C27B0);
  
  // Seat colors
  static const Color seatAvailable = Color(0x4DFFFFFF);
  static const Color seatSelected = Color(0xff2196F3);
  static const Color seatBooked = Color(0xffF44336);
  static const Color seatVip = Color(0xffFFC107);
  static const Color seatCouple = Color(0xffE91E63);
  static const Color seatDisabled = Color(0xff4CAF50);
  
  // Genre selection colors
  static const Color genreSelected = Color(0xffFFC107);
  static const Color genreUnselected = Color(0x1EA6A1E0);
  
  // Status chip colors
  static const Color statusPending = Color(0xff9E9E9E);
  static const Color statusAccepted = Color(0xff2196F3);
  static const Color statusInProgress = Color(0xffFF9800);
  static const Color statusFixed = Color(0xff4CAF50);
  
  // Common gradients
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primaryGradientStart, primaryGradientEnd],
  );
  
  static const LinearGradient primaryGradientVertical = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [primaryGradientStart, primaryGradientEnd],
  );
  
  static const LinearGradient overlayGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Colors.transparent,
      Color(0x4D2B5876),
      Color(0xB34E4376),
    ],
  );
  
  static const LinearGradient genreSelectedGradient = LinearGradient(
    colors: [Color(0xFFFFB74D), Color(0xFFFF9800)],
  );
  
  static const LinearGradient genreUnselectedGradient = LinearGradient(
    colors: [Color(0x1EA6A1E0), Color(0x1EA1F3FE)],
  );
  
  // Helper methods
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }
  
  static Color getChatBackgroundColor({
    required String responderId,
    required bool isFromDeveloper,
    required bool isFromAdmin,
    required bool isCurrentUser,
  }) {
    if (responderId == 'system') {
      return chatSystemBackground.withOpacity(0.3);
    } else if (isFromDeveloper) {
      return chatDeveloperBackground.withOpacity(0.8);
    } else if (isFromAdmin) {
      return chatAdminBackground.withOpacity(0.8);
    } else {
      return isCurrentUser
          ? chatCurrentUserBackground.withOpacity(0.6)
          : chatUserBackground.withOpacity(0.4);
    }
  }
  
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return statusPending;
      case 'accepted':
        return statusAccepted;
      case 'inprogress':
      case 'in_progress':
        return statusInProgress;
      case 'fixed':
        return statusFixed;
      default:
        return statusPending;
    }
  }
  
  static Color getScreenTypeColor(String screenType) {
    switch (screenType.toLowerCase()) {
      case 'standard':
        return screenStandard;
      case 'vip':
        return screenVip;
      case 'imax':
        return screenImax;
      case 'dolby':
        return screenDolby;
      case 'premium':
        return screenPremium;
      default:
        return screenStandard;
    }
  }
}
