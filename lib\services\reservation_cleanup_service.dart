import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';

class ReservationCleanupService extends GetxService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  Timer? _cleanupTimer;

  // Cleanup interval (2 minutes) - faster for realtime experience
  static const Duration _cleanupInterval = Duration(minutes: 2);

  // Reservation timeout (5 minutes) - shorter timeout for better UX
  static const Duration _reservationTimeout = Duration(minutes: 5);

  @override
  void onInit() {
    super.onInit();
    _startCleanupTimer();
  }

  @override
  void onClose() {
    _cleanupTimer?.cancel();
    super.onClose();
  }

  void _startCleanupTimer() {
    _cleanupTimer = Timer.periodic(_cleanupInterval, (timer) {
      _cleanupExpiredReservations();
    });
  }

  Future<void> _cleanupExpiredReservations() async {
    try {
      print('Starting cleanup of expired reservations...');

      // Get all showtimes with reservations
      final showtimesQuery = await _firestore
          .collection('showtimes')
          .where('reservedSeats', isNotEqualTo: []).get();

      int totalCleaned = 0;

      for (final doc in showtimesQuery.docs) {
        try {
          final cleaned =
              await _cleanupShowtimeReservations(doc.id, doc.data());
          totalCleaned += cleaned;
        } catch (e) {
          print('Error cleaning showtime ${doc.id}: $e');
        }
      }

      if (totalCleaned > 0) {
        print('Cleanup completed: $totalCleaned expired reservations removed');
      }
    } catch (e) {
      print('Error during reservation cleanup: $e');
    }
  }

  Future<int> _cleanupShowtimeReservations(
      String showtimeId, Map<String, dynamic> showtimeData) async {
    final currentReservedSeats =
        List<String>.from(showtimeData['reservedSeats'] ?? []);
    final reservationData =
        Map<String, dynamic>.from(showtimeData['reservationData'] ?? {});

    if (currentReservedSeats.isEmpty) {
      return 0;
    }

    final now = DateTime.now();
    final validReservedSeats = <String>[];
    final validReservationData = <String, dynamic>{};
    int cleanedCount = 0;

    for (String seat in currentReservedSeats) {
      final reservationInfo = reservationData[seat];
      if (reservationInfo != null) {
        final reservedAt =
            (reservationInfo['reservedAt'] as Timestamp).toDate();
        if (now.difference(reservedAt) < _reservationTimeout) {
          validReservedSeats.add(seat);
          validReservationData[seat] = reservationInfo;
        } else {
          cleanedCount++;
          print(
              'Cleaning expired reservation for seat $seat in showtime $showtimeId');
        }
      } else {
        // Remove seats without reservation data
        cleanedCount++;
        print(
            'Cleaning seat $seat without reservation data in showtime $showtimeId');
      }
    }

    // Only update if there are changes
    if (cleanedCount > 0) {
      await _firestore.runTransaction((transaction) async {
        final showtimeRef = _firestore.collection('showtimes').doc(showtimeId);
        final showtimeDoc = await transaction.get(showtimeRef);

        if (!showtimeDoc.exists) {
          return;
        }

        transaction.update(showtimeRef, {
          'reservedSeats': validReservedSeats,
          'reservationData': validReservationData,
          'updatedAt': Timestamp.now(),
        });
      });
    }

    return cleanedCount;
  }

  // Manual cleanup for specific showtime
  Future<void> cleanupShowtime(String showtimeId) async {
    try {
      final showtimeDoc =
          await _firestore.collection('showtimes').doc(showtimeId).get();
      if (showtimeDoc.exists) {
        await _cleanupShowtimeReservations(showtimeId, showtimeDoc.data()!);
      }
    } catch (e) {
      print('Error cleaning up showtime $showtimeId: $e');
    }
  }

  // Force cleanup all expired reservations immediately
  Future<void> forceCleanupAll() async {
    await _cleanupExpiredReservations();
  }

  // Get reservation status for debugging
  Future<Map<String, dynamic>> getReservationStatus() async {
    try {
      final showtimesQuery = await _firestore
          .collection('showtimes')
          .where('reservedSeats', isNotEqualTo: []).get();

      int totalReservations = 0;
      int expiredReservations = 0;
      final now = DateTime.now();

      for (final doc in showtimesQuery.docs) {
        final showtimeData = doc.data();
        final currentReservedSeats =
            List<String>.from(showtimeData['reservedSeats'] ?? []);
        final reservationData =
            Map<String, dynamic>.from(showtimeData['reservationData'] ?? {});

        for (String seat in currentReservedSeats) {
          totalReservations++;
          final reservationInfo = reservationData[seat];
          if (reservationInfo != null) {
            final reservedAt =
                (reservationInfo['reservedAt'] as Timestamp).toDate();
            if (now.difference(reservedAt) >= _reservationTimeout) {
              expiredReservations++;
            }
          } else {
            expiredReservations++;
          }
        }
      }

      return {
        'totalReservations': totalReservations,
        'expiredReservations': expiredReservations,
        'validReservations': totalReservations - expiredReservations,
        'lastCleanup': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'error': e.toString(),
      };
    }
  }
}
