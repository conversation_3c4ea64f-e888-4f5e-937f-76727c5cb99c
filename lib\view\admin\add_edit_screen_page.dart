import 'package:flutter/material.dart';
import 'package:get/get.dart' hide ScreenType;
import 'package:google_fonts/google_fonts.dart';
import '../../models/screen_model.dart';
import '../../models/theater_model.dart';
import '../../controllers/screen_controller.dart';

class AddEditScreenPage extends StatefulWidget {
  final ScreenModel? screen; // null for add, non-null for edit
  final String? preSelectedTheaterId;

  const AddEditScreenPage({
    Key? key,
    this.screen,
    this.preSelectedTheaterId,
  }) : super(key: key);

  @override
  State<AddEditScreenPage> createState() => _AddEditScreenPageState();
}

class _AddEditScreenPageState extends State<AddEditScreenPage> {
  final ScreenController _screenController = Get.find<ScreenController>();
  final _formKey = GlobalKey<FormState>();

  // Form controllers
  late TextEditingController _nameController;
  late TextEditingController _rowsController;
  late TextEditingController _seatsPerRowController;

  // Form values
  TheaterModel? selectedTheater;
  ScreenType selectedType = ScreenType.standard;
  List<String> selectedAmenities = [];
  bool isActive = true;

  // Available amenities
  final List<String> availableAmenities = [
    'Air Conditioning',
    'Sound System',
    'Dolby Atmos',
    'IMAX Sound System',
    'Reclining Seats',
    'Large Screen',
    'Premium Seating',
    'Wheelchair Accessible',
    'Food Service',
    'VIP Lounge Access',
  ];

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.screen != null) {
      // Edit mode
      final screen = widget.screen!;
      _nameController = TextEditingController(text: screen.name);
      _rowsController = TextEditingController(text: screen.rows.toString());
      _seatsPerRowController =
          TextEditingController(text: screen.seatsPerRow.toString());

      selectedTheater = _screenController.getTheaterById(screen.theaterId);
      selectedType = screen.type;
      selectedAmenities = List.from(screen.amenities);
      isActive = screen.isActive;
    } else {
      // Add mode
      _nameController = TextEditingController();
      _rowsController = TextEditingController(text: '10');
      _seatsPerRowController = TextEditingController(text: '10');

      // Pre-select theater if provided
      if (widget.preSelectedTheaterId != null) {
        selectedTheater =
            _screenController.getTheaterById(widget.preSelectedTheaterId!);
      }

      selectedAmenities = ['Air Conditioning', 'Sound System'];
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _rowsController.dispose();
    _seatsPerRowController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEdit = widget.screen != null;

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xff2B5876), Color(0xff4E4376)],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                    Expanded(
                      child: Text(
                        isEdit ? 'Sửa Phòng Chiếu' : 'Thêm Phòng Chiếu',
                        style: GoogleFonts.mulish(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Form
              Expanded(
                child: Container(
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: Colors.white.withOpacity(0.2)),
                  ),
                  child: Form(
                    key: _formKey,
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildBasicInfoSection(),
                          const SizedBox(height: 24),
                          _buildSeatingSection(),
                          const SizedBox(height: 24),
                          _buildAmenitiesSection(),
                          const SizedBox(height: 24),
                          if (isEdit) _buildStatusSection(),
                          if (isEdit) const SizedBox(height: 24),
                          _buildActionButtons(),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Thông tin cơ bản',
          style: GoogleFonts.mulish(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 16),

        // Theater selection
        Text(
          'Rạp chiếu',
          style: GoogleFonts.mulish(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.white.withOpacity(0.3)),
          ),
          child: Obx(() => DropdownButton<TheaterModel>(
                value: selectedTheater,
                isExpanded: true,
                dropdownColor: const Color(0xff2B5876),
                style: GoogleFonts.mulish(color: Colors.white),
                underline: const SizedBox(),
                hint: Text(
                  'Chọn rạp chiếu',
                  style:
                      GoogleFonts.mulish(color: Colors.white.withOpacity(0.7)),
                ),
                items: _screenController.theaters.map((theater) {
                  return DropdownMenuItem(
                    value: theater,
                    child: Text(
                      theater.name,
                      style: GoogleFonts.mulish(color: Colors.white),
                    ),
                  );
                }).toList(),
                onChanged: widget.screen != null
                    ? null
                    : (value) {
                        setState(() {
                          selectedTheater = value;
                        });
                      },
              )),
        ),

        const SizedBox(height: 16),

        // Screen name
        Text(
          'Tên phòng chiếu',
          style: GoogleFonts.mulish(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _nameController,
          autofocus: false, // Explicitly disable autofocus
          style: GoogleFonts.mulish(color: Colors.white),
          decoration: InputDecoration(
            hintText: 'Ví dụ: Phòng 1, VIP Room, IMAX Theater',
            hintStyle: GoogleFonts.mulish(color: Colors.white.withOpacity(0.5)),
            filled: true,
            fillColor: Colors.white.withOpacity(0.1),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.white.withOpacity(0.3)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.white.withOpacity(0.3)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.blue),
            ),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Vui lòng nhập tên phòng chiếu';
            }
            return null;
          },
        ),

        const SizedBox(height: 16),

        // Screen type
        Text(
          'Loại phòng chiếu',
          style: GoogleFonts.mulish(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.white.withOpacity(0.3)),
          ),
          child: DropdownButton<ScreenType>(
            value: selectedType,
            isExpanded: true,
            dropdownColor: const Color(0xff2B5876),
            style: GoogleFonts.mulish(color: Colors.white),
            underline: const SizedBox(),
            items: ScreenType.values.map((type) {
              return DropdownMenuItem(
                value: type,
                child: Row(
                  children: [
                    Icon(type.icon, color: type.color, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      type.displayName,
                      style: GoogleFonts.mulish(color: Colors.white),
                    ),
                  ],
                ),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  selectedType = value;
                  _updateAmenitiesForType(value);
                });
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSeatingSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Cấu hình ghế ngồi',
          style: GoogleFonts.mulish(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 16),

        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Số hàng',
                    style: GoogleFonts.mulish(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _rowsController,
                    keyboardType: TextInputType.number,
                    style: GoogleFonts.mulish(color: Colors.white),
                    decoration: InputDecoration(
                      hintText: '10',
                      hintStyle: GoogleFonts.mulish(
                          color: Colors.white.withOpacity(0.5)),
                      filled: true,
                      fillColor: Colors.white.withOpacity(0.1),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide:
                            BorderSide(color: Colors.white.withOpacity(0.3)),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide:
                            BorderSide(color: Colors.white.withOpacity(0.3)),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: Colors.blue),
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Nhập số hàng';
                      }
                      final rows = int.tryParse(value);
                      if (rows == null || rows <= 0 || rows > 20) {
                        return 'Từ 1-20 hàng';
                      }
                      return null;
                    },
                    onChanged: (value) => _updateTotalSeats(),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Ghế/hàng',
                    style: GoogleFonts.mulish(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _seatsPerRowController,
                    keyboardType: TextInputType.number,
                    style: GoogleFonts.mulish(color: Colors.white),
                    decoration: InputDecoration(
                      hintText: '10',
                      hintStyle: GoogleFonts.mulish(
                          color: Colors.white.withOpacity(0.5)),
                      filled: true,
                      fillColor: Colors.white.withOpacity(0.1),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide:
                            BorderSide(color: Colors.white.withOpacity(0.3)),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide:
                            BorderSide(color: Colors.white.withOpacity(0.3)),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: Colors.blue),
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Nhập số ghế';
                      }
                      final seats = int.tryParse(value);
                      if (seats == null || seats <= 0 || seats > 30) {
                        return 'Từ 1-30 ghế';
                      }
                      return null;
                    },
                    onChanged: (value) => _updateTotalSeats(),
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Total seats display
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue.withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue.withOpacity(0.5)),
          ),
          child: Row(
            children: [
              const Icon(Icons.event_seat, color: Colors.blue),
              const SizedBox(width: 8),
              Text(
                'Tổng số ghế: ${_calculateTotalSeats()}',
                style: GoogleFonts.mulish(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAmenitiesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tiện ích',
          style: GoogleFonts.mulish(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: availableAmenities.map((amenity) {
            final isSelected = selectedAmenities.contains(amenity);
            return GestureDetector(
              onTap: () {
                setState(() {
                  if (isSelected) {
                    selectedAmenities.remove(amenity);
                  } else {
                    selectedAmenities.add(amenity);
                  }
                });
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color:
                      isSelected ? Colors.blue : Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected
                        ? Colors.blue
                        : Colors.white.withOpacity(0.3),
                  ),
                ),
                child: Text(
                  amenity,
                  style: GoogleFonts.mulish(
                    color: isSelected
                        ? Colors.white
                        : Colors.white.withOpacity(0.8),
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildStatusSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Trạng thái',
          style: GoogleFonts.mulish(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.white.withOpacity(0.3)),
          ),
          child: Row(
            children: [
              Icon(
                isActive ? Icons.check_circle : Icons.pause_circle,
                color: isActive ? Colors.green : Colors.orange,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isActive ? 'Đang hoạt động' : 'Tạm dừng hoạt động',
                      style: GoogleFonts.mulish(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      isActive
                          ? 'Phòng chiếu có thể được sử dụng để tạo lịch chiếu'
                          : 'Phòng chiếu sẽ không hiển thị trong danh sách lịch chiếu',
                      style: GoogleFonts.mulish(
                        color: Colors.white.withOpacity(0.7),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              Switch(
                value: isActive,
                onChanged: (value) {
                  setState(() {
                    isActive = value;
                  });
                },
                activeColor: Colors.green,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    final isEdit = widget.screen != null;

    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Get.back(),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.white,
              side: const BorderSide(color: Colors.white),
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: Text(
              'Hủy',
              style: GoogleFonts.mulish(fontWeight: FontWeight.w600),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Obx(() => ElevatedButton(
                onPressed:
                    _screenController.isLoading.value ? null : _saveScreen,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: _screenController.isLoading.value
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        isEdit ? 'Cập nhật' : 'Tạo phòng chiếu',
                        style: GoogleFonts.mulish(fontWeight: FontWeight.bold),
                      ),
              )),
        ),
      ],
    );
  }

  // Helper methods
  void _updateAmenitiesForType(ScreenType type) {
    switch (type) {
      case ScreenType.standard:
        selectedAmenities = ['Air Conditioning', 'Sound System'];
        break;
      case ScreenType.vip:
        selectedAmenities = [
          'Air Conditioning',
          'Sound System',
          'Reclining Seats',
          'Premium Seating'
        ];
        break;
      case ScreenType.imax:
        selectedAmenities = [
          'Air Conditioning',
          'IMAX Sound System',
          'Large Screen'
        ];
        break;
      case ScreenType.dolby:
        selectedAmenities = [
          'Air Conditioning',
          'Dolby Atmos',
          'Premium Seating'
        ];
        break;
      case ScreenType.premium:
        selectedAmenities = [
          'Air Conditioning',
          'Sound System',
          'Premium Seating',
          'VIP Lounge Access'
        ];
        break;
    }
  }

  void _updateTotalSeats() {
    setState(() {});
  }

  int _calculateTotalSeats() {
    final rows = int.tryParse(_rowsController.text) ?? 0;
    final seatsPerRow = int.tryParse(_seatsPerRowController.text) ?? 0;
    return rows * seatsPerRow;
  }

  List<SeatRowModel> _generateSeatLayout() {
    final rows = int.tryParse(_rowsController.text) ?? 0;
    final seatsPerRow = int.tryParse(_seatsPerRowController.text) ?? 0;

    final seatRows = <SeatRowModel>[];

    for (int i = 0; i < rows; i++) {
      final rowLetter = String.fromCharCode(65 + i); // A, B, C, etc.
      final seats = <SeatModel>[];

      for (int j = 1; j <= seatsPerRow; j++) {
        seats.add(SeatModel(
          number: j.toString(),
          type: _getSeatTypeForPosition(j, seatsPerRow),
          isAvailable: true,
        ));
      }

      seatRows.add(SeatRowModel(
        row: rowLetter,
        seats: seats,
      ));
    }

    return seatRows;
  }

  SeatType _getSeatTypeForPosition(int seatNumber, int totalSeatsInRow) {
    // First and last 2 seats are couple seats for VIP/Premium screens
    if (selectedType == ScreenType.vip || selectedType == ScreenType.premium) {
      if (seatNumber <= 2 || seatNumber >= totalSeatsInRow - 1) {
        return SeatType.couple;
      }
    }

    return SeatType.standard;
  }

  Future<void> _saveScreen() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (selectedTheater == null) {
      Get.snackbar('Lỗi', 'Vui lòng chọn rạp chiếu');
      return;
    }

    final rows = int.parse(_rowsController.text);
    final seatsPerRow = int.parse(_seatsPerRowController.text);
    final totalSeats = rows * seatsPerRow;

    // Validate using controller
    if (!_screenController.validateScreenData(
      name: _nameController.text.trim(),
      theaterId: selectedTheater!.id,
      type: selectedType,
      rows: rows,
      seatsPerRow: seatsPerRow,
      screenId: widget.screen?.id,
    )) {
      return;
    }

    final screenData = ScreenModel(
      id: widget.screen?.id ?? '',
      theaterId: selectedTheater!.id,
      name: _nameController.text.trim(),
      type: selectedType,
      totalSeats: totalSeats,
      rows: rows,
      seatsPerRow: seatsPerRow,
      seatLayout: _generateSeatLayout(),
      amenities: selectedAmenities,
      isActive: isActive,
      createdAt: widget.screen?.createdAt ?? DateTime.now(),
      updatedAt: DateTime.now(),
    );

    bool success;
    if (widget.screen != null) {
      // Update existing screen
      success = await _screenController.updateScreen(screenData);
    } else {
      // Create new screen
      success = await _screenController.createScreen(screenData);
    }

    if (success) {
      Get.back();
    }
  }
}
