import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:movie_finder/models/ticket_model.dart';
import 'package:movie_finder/view/page/ticket_detail_page.dart';

void main() {
  group('TicketDetailPage Tests', () {
    late Ticket testTicket;

    setUp(() {
      // Create a test ticket
      testTicket = Ticket(
        id: 'test_ticket_001',
        userId: 'test_user',
        movieId: 123,
        movieTitle: 'Test Movie',
        moviePosterPath: '/test_poster.jpg',
        theaterId: 'theater_001',
        theaterName: 'Test Theater',
        screenId: 'screen_001',
        screenName: 'Screen 1',
        showtimeId: 'showtime_001',
        date: '2024-01-15',
        time: '19:30',
        seats: [
          TicketSeat(row: 'A', number: '1', type: 'standard', price: 100000),
          TicketSeat(row: 'A', number: '2', type: 'standard', price: 100000),
        ],
        totalPrice: 200000,
        discountApplied: 20000,
        finalPrice: 180000,
        paymentMethod: 'paypal',
        bookingCode: 'BOOK123456',
        status: TicketStatus.confirmed,
        purchaseDate: DateTime.now(),
        loyaltyPointsEarned: 18,
      );
    });

    testWidgets('should display ticket details correctly', (WidgetTester tester) async {
      // Initialize GetX
      Get.testMode = true;

      await tester.pumpWidget(
        GetMaterialApp(
          home: TicketDetailPage(ticket: testTicket),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Verify movie title is displayed
      expect(find.text('Test Movie'), findsOneWidget);

      // Verify theater name is displayed
      expect(find.text('Test Theater'), findsOneWidget);

      // Verify screen name is displayed
      expect(find.text('Screen 1'), findsOneWidget);

      // Verify booking code is displayed
      expect(find.text('BOOK123456'), findsOneWidget);

      // Verify status is displayed
      expect(find.text('Đã xác nhận'), findsOneWidget);

      // Verify QR code section exists
      expect(find.text('Mã QR Vé'), findsOneWidget);
    });

    testWidgets('should display payment method correctly', (WidgetTester tester) async {
      Get.testMode = true;

      await tester.pumpWidget(
        GetMaterialApp(
          home: TicketDetailPage(ticket: testTicket),
        ),
      );

      await tester.pumpAndSettle();

      // Verify PayPal payment method is displayed in Vietnamese
      expect(find.text('PayPal'), findsOneWidget);
    });

    testWidgets('should display seat information correctly', (WidgetTester tester) async {
      Get.testMode = true;

      await tester.pumpWidget(
        GetMaterialApp(
          home: TicketDetailPage(ticket: testTicket),
        ),
      );

      await tester.pumpAndSettle();

      // Verify seats are displayed correctly
      expect(find.text('A1, A2'), findsOneWidget);
    });

    testWidgets('should display price information correctly', (WidgetTester tester) async {
      Get.testMode = true;

      await tester.pumpWidget(
        GetMaterialApp(
          home: TicketDetailPage(ticket: testTicket),
        ),
      );

      await tester.pumpAndSettle();

      // Verify final price is displayed
      expect(find.textContaining('180.000'), findsOneWidget);
    });

    testWidgets('should handle back navigation', (WidgetTester tester) async {
      Get.testMode = true;

      await tester.pumpWidget(
        GetMaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => TicketDetailPage(ticket: testTicket),
                  ),
                ),
                child: const Text('Go to Detail'),
              ),
            ),
          ),
        ),
      );

      // Navigate to detail page
      await tester.tap(find.text('Go to Detail'));
      await tester.pumpAndSettle();

      // Verify we're on the detail page
      expect(find.text('Chi Tiết Vé'), findsOneWidget);

      // Tap back button
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Verify we're back to the original page
      expect(find.text('Go to Detail'), findsOneWidget);
    });

    testWidgets('should copy booking code to clipboard', (WidgetTester tester) async {
      Get.testMode = true;

      await tester.pumpWidget(
        GetMaterialApp(
          home: TicketDetailPage(ticket: testTicket),
        ),
      );

      await tester.pumpAndSettle();

      // Find and tap the copy icon
      final copyIcon = find.byIcon(Icons.copy);
      expect(copyIcon, findsOneWidget);

      await tester.tap(copyIcon);
      await tester.pumpAndSettle();

      // Verify snackbar appears (GetX snackbar)
      expect(find.text('Đã sao chép'), findsOneWidget);
    });

    test('should generate correct QR data', () {
      final ticketDetailPage = TicketDetailPage(ticket: testTicket);
      
      // Access the private method through reflection or create a public test method
      // For now, we'll test the expected format
      final expectedQRData = 'TICKET:BOOK123456|MOVIE:Test Movie|DATE:2024-01-15|TIME:19:30|THEATER:Test Theater|SCREEN:Screen 1|SEATS:A1, A2|PRICE:180000.0';
      
      // This would be the expected QR data format
      expect(expectedQRData.contains('BOOK123456'), isTrue);
      expect(expectedQRData.contains('Test Movie'), isTrue);
      expect(expectedQRData.contains('2024-01-15'), isTrue);
      expect(expectedQRData.contains('19:30'), isTrue);
    });
  });
}
