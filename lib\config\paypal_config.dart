class PayPalConfig {
  // PayPal Sandbox Configuration
  // These are working sandbox credentials for testing
  static const String sandboxClientId =
      'AX2fmdCPYyB24TsYsZAf-VbOonTGOi9wfS86KmabwgBZp897S5SoCpKru4GT4ZTObmb4qAg67KJwToiO';
  static const String sandboxSecretKey =
      'EOumV9HjGcIfvWFbBdvUBNVkkm-UQT6POlx4BR3mk8PEk8lnsQiA05Z1Rk3OcRM0sardEdmiBQUwBQqb';

  // PayPal Production Configuration (for live environment)
  static const String productionClientId = 'YOUR_PRODUCTION_CLIENT_ID_HERE';
  static const String productionSecretKey = 'YOUR_PRODUCTION_SECRET_KEY_HERE';

  // Environment setting
  static const bool useSandbox = true; // Set to false for production

  // Get current client ID based on environment
  static String get clientId =>
      useSandbox ? sandboxClientId : productionClientId;

  // Get current secret key based on environment
  static String get secretKey =>
      useSandbox ? sandboxSecretKey : productionSecretKey;

  // Check if PayPal is properly configured
  static bool get isConfigured {
    final currentClientId = clientId;
    final currentSecretKey = secretKey;

    if (useSandbox) {
      // For sandbox, we have working credentials
      return currentClientId.isNotEmpty && currentSecretKey.isNotEmpty;
    } else {
      // For production, check if credentials are set
      return currentClientId.isNotEmpty &&
          currentSecretKey.isNotEmpty &&
          currentClientId != 'YOUR_PRODUCTION_CLIENT_ID_HERE' &&
          currentSecretKey != 'YOUR_PRODUCTION_SECRET_KEY_HERE';
    }
  }

  // Get environment name
  static String get environmentName => useSandbox ? 'Sandbox' : 'Production';

  // PayPal setup instructions
  static const String setupInstructions = '''
To set up PayPal integration:

1. Go to https://developer.paypal.com/
2. Log in with your PayPal account
3. Create a new app in the sandbox environment
4. Copy the Client ID and Secret Key
5. Replace the values in lib/config/paypal_config.dart:
   - sandboxClientId: Your sandbox client ID
   - sandboxSecretKey: Your sandbox secret key

For testing, you can use PayPal sandbox test accounts:
- Buyer account: Create a personal sandbox account
- Seller account: Create a business sandbox account

Test credit card numbers:
- Visa: ****************
- MasterCard: ****************
- American Express: ***************
''';
}
