# Giải Pháp Xử Lý Concurrent Booking (Đặt Vé Đồng Thời)

## Vấn Đề
Khi 2 người dùng cùng lúc đặt cùng 1 chỗ ngồi, hệ thống cần đảm bảo chỉ 1 người có thể đặt thành công để tránh xung đột dữ liệu.

## Giải Pháp Đã Triển Khai

### 1. Firestore Transactions
- **Vấn đề cũ**: Sử dụng `update()` đơn giản có thể gây race condition
- **Giải pháp mới**: Sử dụng `runTransaction()` để đảm bảo atomic operations

```dart
await _firestore.runTransaction((transaction) async {
  // Đọc dữ liệu hiện tại
  final showtimeDoc = await transaction.get(showtimeRef);
  
  // Kiểm tra ghế có còn available không
  for (String seatId in seatIds) {
    if (currentBookedSeats.contains(seatId)) {
      throw Exception('Ghế $seatId đã được đặt bởi người khác');
    }
  }
  
  // Cập nhật dữ liệu
  transaction.update(showtimeRef, updatedData);
});
```

### 2. Hệ Thống Reservation (Giữ Ghế Realtime)
- **Thời gian giữ**: 5 phút (giảm từ 10 phút)
- **Tự động cleanup**: Mỗi 2 phút (tăng từ 5 phút)
- **User-specific**: Mỗi reservation gắn với userId
- **Realtime release**: Ghế được release ngay khi user bỏ chọn

#### Cấu Trúc Dữ Liệu:
```json
{
  "reservedSeats": ["A1", "A2"],
  "reservationData": {
    "A1": {
      "userId": "user123",
      "reservedAt": "2024-01-01T10:00:00Z"
    },
    "A2": {
      "userId": "user123", 
      "reservedAt": "2024-01-01T10:00:00Z"
    }
  }
}
```

### 3. Quy Trình Đặt Vé

#### Bước 1: Chọn Ghế
```dart
// Kiểm tra ghế available
bool isSeatAvailable(String seatId) {
  return !showtime.bookedSeats.contains(seatId) && 
         !showtime.reservedSeats.contains(seatId);
}
```

#### Bước 2: Reserve Ghế (Khi nhấn "Tiếp tục thanh toán")
```dart
await _showtimeService.reserveSeats(
  showtimeId, 
  selectedSeats,
  userId: currentUserId,
);
```

#### Bước 3: Thanh Toán và Book Ghế
```dart
// Sau khi thanh toán thành công
await _showtimeService.bookSeats(showtimeId, selectedSeats);
```

### 4. Xử Lý Lỗi Concurrent Booking

#### Các Loại Lỗi:
1. **Ghế đã được đặt**: `"Ghế A1 đã được đặt bởi người khác"`
2. **Ghế đang được giữ**: `"Ghế A1 đang được giữ bởi người khác"`
3. **Reservation hết hạn**: Tự động cleanup và cho phép đặt lại

#### Error Handling:
```dart
try {
  await reserveSeats(showtimeId, seatIds, userId: userId);
} catch (e) {
  if (e.toString().contains('đã được đặt')) {
    // Hiển thị message và refresh seat map
    showErrorAndRefresh('Ghế đã được đặt bởi người khác');
  } else if (e.toString().contains('đang được giữ')) {
    // Hiển thị message và suggest retry
    showErrorAndRefresh('Ghế đang được giữ, vui lòng thử lại sau');
  }
}
```

### 5. Automatic Cleanup Service

#### ReservationCleanupService:
- **Chạy mỗi**: 2 phút (tăng tần suất)
- **Cleanup**: Reservations > 5 phút (giảm timeout)
- **Background**: Không ảnh hưởng UX
- **Realtime**: Ghế được release ngay khi user bỏ chọn

```dart
class ReservationCleanupService extends GetxService {
  Timer? _cleanupTimer;
  static const Duration _cleanupInterval = Duration(minutes: 5);
  static const Duration _reservationTimeout = Duration(minutes: 10);
  
  void _startCleanupTimer() {
    _cleanupTimer = Timer.periodic(_cleanupInterval, (timer) {
      _cleanupExpiredReservations();
    });
  }
}
```

### 6. Real-time Updates

#### Refresh Mechanism:
- **Khi có lỗi**: Tự động refresh seat map
- **Cleanup expired**: Xóa selections và reload
- **User feedback**: Clear error messages

```dart
Future<void> _refreshShowtimeData() async {
  // Clear selected seats
  _selectedSeats.clear();
  _totalPrice.value = 0.0;
  
  // Reload screen data
  await _loadScreen();
  
  // Clean up expired reservations
  await _showtimeService.cleanupExpiredReservations(showtimeId);
}
```

## Kết Quả

### Trước Khi Cải Thiện:
- ❌ Race condition khi 2 người đặt cùng lúc
- ❌ Có thể double-booking
- ❌ Không có timeout cho seat selection
- ❌ Error handling kém

### Sau Khi Cải Thiện:
- ✅ Firestore transactions đảm bảo atomic operations
- ✅ Chỉ 1 người có thể book được ghế
- ✅ Reservation system với timeout 5 phút (realtime)
- ✅ Automatic cleanup expired reservations (mỗi 2 phút)
- ✅ Clear error messages và user feedback
- ✅ Real-time seat status updates
- ✅ Instant release khi user bỏ chọn ghế

## Luồng Xử Lý Concurrent Booking (Realtime)

```
Scenario 1: User A thanh toán thành công
User A chọn ghế A1 → Reserve thành công (10:00)
User B chọn ghế A1 → Lỗi "đang được giữ" (10:01)
User A thanh toán → Book thành công (10:05)
User B thử lại → Lỗi "đã được đặt" (10:06)

Scenario 2: User A bỏ chọn ghế (REALTIME)
User A chọn ghế A1 → Reserve thành công (10:00)
User B chọn ghế A1 → Lỗi "đang được giữ" (10:01)
User A bỏ chọn ghế A1 → Release ngay lập tức (10:02)
User B chọn ghế A1 → Reserve thành công (10:02)

Scenario 3: Timeout tự động
User A chọn ghế A1 → Reserve thành công (10:00)
User B chọn ghế A1 → Lỗi "đang được giữ" (10:01)
User A không làm gì → Auto cleanup (10:05)
User B thử lại → Reserve thành công (10:05)
```

## Testing

Để test concurrent booking:
1. Mở 2 devices/browsers
2. Đăng nhập 2 accounts khác nhau
3. Cùng chọn 1 ghế trong cùng showtime
4. Kiểm tra chỉ 1 người book được thành công

## Monitoring

- Check reservation status: `ReservationCleanupService.getReservationStatus()`
- Manual cleanup: `ReservationCleanupService.forceCleanupAll()`
- Debug logs trong console cho mọi booking operations
