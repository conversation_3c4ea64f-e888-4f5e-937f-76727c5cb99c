# PayPal Integration Guide - Đớp Phim App

## Tổng Quan

Ứng dụng Đớp Phim đã được tích hợp PayPal sandbox để xử lý thanh toán vé xem phim. Hệ thống hỗ trợ cả thanh toán thật qua PayPal và simulation để test.

## Cấu Hình PayPal

### 1. PayPal Developer Account Setup

1. <PERSON><PERSON><PERSON> cập [PayPal Developer](https://developer.paypal.com/)
2. Đăng nhập bằng tài khoản PayPal của bạn
3. Tạo một ứng dụng mới trong sandbox environment
4. Lấy Client ID và Secret Key

### 2. Cấu Hình Trong App

File cấu hình: `lib/config/paypal_config.dart`

```dart
class PayPalConfig {
  // Sandbox credentials (đã có sẵn cho testing)
  static const String sandboxClientId = 'AX2fmdCPYyB24TsYsZAf-VbOonTGOi9wfS86KmabwgBZp897S5SoCpKru4GT4ZTObmb4qAg67KJwToiO';
  static const String sandboxSecretKey = 'EOumV9HjGcIfvWFbBdvUBNVkkm-UQT6POlx4BR3mk8PEk8lnsQiA05Z1Rk3OcRM0sardEdmiBQUwBQqb';
  
  // Production credentials (cần thay đổi cho production)
  static const String productionClientId = 'YOUR_PRODUCTION_CLIENT_ID_HERE';
  static const String productionSecretKey = 'YOUR_PRODUCTION_SECRET_KEY_HERE';
  
  // Environment setting
  static const bool useSandbox = true; // Set to false for production
}
```

## Tính Năng Đã Triển Khai

### 1. PayPal Service (`lib/services/paypal_service.dart`)

- **processPayment()**: Xử lý thanh toán PayPal thật
- **simulatePayment()**: Mô phỏng thanh toán để test
- **convertVndToUsd()**: Chuyển đổi VND sang USD (PayPal không hỗ trợ VND)
- **isConfigured()**: Kiểm tra cấu hình PayPal
- **getEnvironmentInfo()**: Lấy thông tin môi trường

### 2. Payment Service Integration

File: `lib/services/payment_service.dart`

- Tích hợp PayPal vào hệ thống thanh toán hiện có
- Tự động phát hiện phương thức thanh toán PayPal
- Xử lý context cho PayPal UI
- Error handling cho PayPal payments

### 3. Payment Model Updates

File: `lib/models/payment_model.dart`

- Thêm `PaymentMethod.paypal`
- Cập nhật tất cả switch statements
- Thêm icon và display name cho PayPal

### 4. PayPal Test Page

File: `lib/view/admin/paypal_test_page.dart`

- Giao diện test PayPal payments
- Hiển thị trạng thái cấu hình
- Test cả real payment và simulation
- Chuyển đổi VND sang USD tự động

## Cách Sử Dụng

### 1. Cho Developer/Admin

1. Đăng nhập với tài khoản developer
2. Vào Admin Dashboard
3. Chọn "PayPal Test"
4. Nhập số tiền (VND) và mô tả
5. Chọn "Test PayPal Payment" hoặc "Test Simulation"

### 2. Cho User

1. Chọn phim và ghế
2. Tại trang thanh toán, chọn "PayPal"
3. Nhấn "Thanh toán"
4. Sẽ được chuyển đến PayPal để hoàn tất thanh toán

## Luồng Thanh Toán PayPal

```
1. User chọn PayPal làm phương thức thanh toán
2. App chuyển đổi VND → USD (tỷ giá ~24,000 VND = 1 USD)
3. Tạo PayPal payment request
4. Mở PayPal webview để user đăng nhập và thanh toán
5. PayPal trả về kết quả
6. App xử lý kết quả và cập nhật database
7. Nếu thành công: book ghế và tạo vé
8. Nếu thất bại: release ghế và thông báo lỗi
```

## Test Accounts

### PayPal Sandbox Test Accounts

Bạn có thể tạo test accounts tại [PayPal Sandbox](https://developer.paypal.com/developer/accounts/):

**Personal Account (Buyer):**
- Email: <EMAIL>
- Password: test123456

**Business Account (Seller):**
- Email: <EMAIL>  
- Password: test123456

### Test Credit Cards

- **Visa**: ****************
- **MasterCard**: ****************
- **American Express**: ***************

## Xử Lý Lỗi

### Các Lỗi Thường Gặp

1. **"PayPal is not configured"**
   - Kiểm tra Client ID và Secret Key trong config
   - Đảm bảo credentials không phải placeholder

2. **"Payment was cancelled"**
   - User đã hủy thanh toán trên PayPal
   - App sẽ tự động release ghế đã reserve

3. **"Context is required for PayPal payments"**
   - Lỗi technical, cần context để mở PayPal UI

### Error Handling Flow

```dart
try {
  final result = await PayPalService.processPayment(...);
  if (result['success'] == true) {
    // Thanh toán thành công
    await bookSeats();
    navigateToSuccess();
  } else {
    // Thanh toán thất bại
    await releaseSeats();
    showError(result['error']);
  }
} catch (e) {
  // Lỗi technical
  await releaseSeats();
  showError('Payment error: $e');
}
```

## Production Deployment

### Checklist Trước Khi Deploy

1. ✅ Thay đổi `useSandbox = false` trong PayPalConfig
2. ✅ Cập nhật production Client ID và Secret Key
3. ✅ Test thanh toán với tài khoản thật
4. ✅ Kiểm tra tỷ giá VND/USD (cập nhật từ API thật)
5. ✅ Cấu hình webhook để nhận thông báo từ PayPal

### Security Notes

- Không commit production credentials vào git
- Sử dụng environment variables cho production
- Implement proper webhook verification
- Log tất cả transactions để audit

## Monitoring & Analytics

### Metrics Cần Theo Dõi

- PayPal payment success rate
- Average payment processing time
- Currency conversion accuracy
- User drop-off rate tại PayPal

### Logging

App tự động log:
- PayPal payment attempts
- Success/failure results
- Error messages
- Transaction IDs

## Support & Troubleshooting

### Debug Tools

1. PayPal Test Page (chỉ cho developer)
2. Payment logs trong Firestore
3. PayPal Developer Dashboard
4. App error logs

### Common Issues

1. **Slow PayPal loading**: Kiểm tra network connection
2. **Currency mismatch**: Đảm bảo sử dụng USD cho PayPal
3. **Webhook failures**: Kiểm tra PayPal webhook configuration

## Future Enhancements

- [ ] Real-time currency conversion API
- [ ] PayPal subscription cho membership
- [ ] PayPal Express Checkout
- [ ] Multiple currency support
- [ ] PayPal webhook integration
- [ ] Advanced fraud detection
