import 'package:flutter/material.dart';
import 'package:flutter_paypal_payment/flutter_paypal_payment.dart';

class PayPalPaymentWrapper extends StatefulWidget {
  final bool sandboxMode;
  final String clientId;
  final String secretKey;
  final List<Map<String, dynamic>> transactions;
  final String note;

  const PayPalPaymentWrapper({
    Key? key,
    required this.sandboxMode,
    required this.clientId,
    required this.secretKey,
    required this.transactions,
    required this.note,
  }) : super(key: key);

  @override
  State<PayPalPaymentWrapper> createState() => _PayPalPaymentWrapperState();
}

class _PayPalPaymentWrapperState extends State<PayPalPaymentWrapper> {
  bool _hasReturned = false;

  void _handleSuccess(Map params) {
    if (!_hasReturned) {
      _hasReturned = true;
      print("PayPal payment success: $params");

      // Use a post-frame callback to ensure the navigation happens after the current frame
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          Navigator.of(context).pop(params);
        }
      });
    }
  }

  void _handleError(dynamic error) {
    if (!_hasReturned) {
      _hasReturned = true;
      print("PayPal payment error: $error");

      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          Navigator.of(context).pop({'error': error});
        }
      });
    }
  }

  void _handleCancel() {
    if (!_hasReturned) {
      _hasReturned = true;
      print("PayPal payment cancelled");

      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          Navigator.of(context).pop({'cancelled': true});
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        if (!didPop && !_hasReturned) {
          _hasReturned = true;
          Navigator.of(context).pop({'cancelled': true});
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('PayPal Payment'),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              if (!_hasReturned) {
                _hasReturned = true;
                Navigator.of(context).pop({'cancelled': true});
              }
            },
          ),
        ),
        body: PaypalCheckoutView(
          sandboxMode: widget.sandboxMode,
          clientId: widget.clientId,
          secretKey: widget.secretKey,
          transactions: widget.transactions,
          note: widget.note,
          onSuccess: _handleSuccess,
          onError: _handleError,
          onCancel: _handleCancel,
        ),
      ),
    );
  }
}
