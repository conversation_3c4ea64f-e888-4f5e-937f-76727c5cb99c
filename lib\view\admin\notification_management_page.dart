import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/notification_controller.dart';
import '../../models/user_model.dart';
import '../../models/notification_model.dart';
import '../../services/notification_service.dart';
import '../../utils/role_helper.dart';

enum NotificationType {
  promo,
  ticket,
  system,
  movie,
}

extension NotificationTypeExtension on NotificationType {
  String get displayName {
    switch (this) {
      case NotificationType.promo:
        return '<PERSON><PERSON><PERSON>ến mãi';
      case NotificationType.ticket:
        return 'Vé';
      case NotificationType.system:
        return 'Hệ thống';
      case NotificationType.movie:
        return 'Phim mới';
    }
  }

  IconData get icon {
    switch (this) {
      case NotificationType.promo:
        return Icons.local_offer;
      case NotificationType.ticket:
        return Icons.confirmation_number;
      case NotificationType.system:
        return Icons.system_update;
      case NotificationType.movie:
        return Icons.movie;
    }
  }

  Color get color {
    switch (this) {
      case NotificationType.promo:
        return Colors.orange;
      case NotificationType.ticket:
        return Colors.green;
      case NotificationType.system:
        return Colors.blue;
      case NotificationType.movie:
        return Colors.purple;
    }
  }
}

class NotificationManagementPage extends StatefulWidget {
  const NotificationManagementPage({Key? key}) : super(key: key);

  @override
  State<NotificationManagementPage> createState() =>
      _NotificationManagementPageState();
}

class _NotificationManagementPageState
    extends State<NotificationManagementPage> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AuthController _authController = Get.find<AuthController>();

  final RxBool _isLoading = false.obs;
  final RxList<DocumentSnapshot> _notifications = <DocumentSnapshot>[].obs;

  @override
  void initState() {
    super.initState();
    _loadNotifications();
  }

  Future<void> _loadNotifications() async {
    _isLoading.value = true;

    try {
      final snapshot = await _firestore
          .collection('notifications')
          .orderBy('createdAt', descending: true)
          .get();

      _notifications.value = snapshot.docs;
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to load notifications: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> _deleteNotification(String id) async {
    try {
      await _firestore.collection('notifications').doc(id).delete();

      Get.snackbar(
        'Success',
        'Notification deleted successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      _loadNotifications();
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to delete notification: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Kiểm tra quyền truy cập
    if (!RoleHelper.hasAdminAccess()) {
      // Nếu không có quyền, chuyển hướng về trang chính
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.back();
        Get.snackbar(
          'Không có quyền truy cập',
          'Bạn không có quyền truy cập trang này',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      });

      // Hiển thị màn hình loading trong khi chuyển hướng
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Quản lý thông báo',
          style: GoogleFonts.mulish(
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadNotifications,
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddNotificationDialog(context),
        child: const Icon(Icons.add),
      ),
      body: SafeArea(
        child: Obx(() {
          if (_isLoading.value) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (_notifications.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.notifications_off_outlined,
                    size: 64,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Không có thông báo nào',
                    style: GoogleFonts.mulish(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Nhấn nút + để thêm thông báo mới',
                    style: GoogleFonts.mulish(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            itemCount: _notifications.length,
            padding: const EdgeInsets.all(16),
            itemBuilder: (context, index) {
              final notification = _notifications[index];
              final data = notification.data() as Map<String, dynamic>;

              final title = data['title'] ?? 'No Title';
              final message = data['message'] ?? 'No Message';
              final typeStr = data['type'] ?? 'system';
              final createdAt = (data['createdAt'] as Timestamp).toDate();

              final type = NotificationType.values.firstWhere(
                (e) => e.toString().split('.').last == typeStr,
                orElse: () => NotificationType.system,
              );

              final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

              return Card(
                margin: const EdgeInsets.only(bottom: 12),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor: type.color.withOpacity(0.2),
                    child: Icon(
                      type.icon,
                      color: type.color,
                    ),
                  ),
                  title: Text(
                    title,
                    style: GoogleFonts.mulish(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        message,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${type.displayName} • ${dateFormat.format(createdAt)}',
                        style: GoogleFonts.mulish(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                  trailing: IconButton(
                    icon: const Icon(Icons.delete),
                    onPressed: () => _deleteNotification(notification.id),
                  ),
                  onTap: () => _showNotificationDetails(context, notification),
                ),
              );
            },
          );
        }),
      ),
    );
  }

  void _showNotificationDetails(
      BuildContext context, DocumentSnapshot notification) {
    final data = notification.data() as Map<String, dynamic>;

    final title = data['title'] ?? 'No Title';
    final message = data['message'] ?? 'No Message';
    final typeStr = data['type'] ?? 'system';
    final createdAt = (data['createdAt'] as Timestamp).toDate();

    final type = NotificationType.values.firstWhere(
      (e) => e.toString().split('.').last == typeStr,
      orElse: () => NotificationType.system,
    );

    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  type.icon,
                  color: type.color,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  type.displayName,
                  style: GoogleFonts.mulish(
                    color: type.color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              dateFormat.format(createdAt),
              style: GoogleFonts.mulish(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 16),
            Text(message),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Đóng'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteNotification(notification.id);
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Xóa'),
          ),
        ],
      ),
    );
  }

  void _showAddNotificationDialog(BuildContext context) {
    final titleController = TextEditingController();
    final bodyController = TextEditingController();
    final imageUrlController = TextEditingController();
    final targetScreenController = TextEditingController();

    NotificationType selectedType = NotificationType.system;
    bool isPublic = true;
    List<String> selectedUserIds = [];
    List<UserModel> users = [];
    bool isLoadingUsers = false;

    // Ngày hết hạn mặc định là 30 ngày từ hiện tại
    DateTime expiresAt = DateTime.now().add(const Duration(days: 30));

    // Dịch vụ thông báo
    final notificationService = NotificationService();

    // Load users for the dropdown
    Future<void> loadUsers() async {
      try {
        setState(() {
          isLoadingUsers = true;
        });
        users = await _authController.getAllUsers();
      } catch (e) {
        Get.snackbar(
          'Error',
          'Failed to load users: $e',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      } finally {
        setState(() {
          isLoadingUsers = false;
        });
      }
    }

    // Load users immediately
    loadUsers();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Thêm thông báo mới'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: titleController,
                  autofocus: false, // Explicitly disable autofocus
                  decoration: const InputDecoration(
                    labelText: 'Tiêu đề',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: bodyController,
                  autofocus: false, // Explicitly disable autofocus
                  decoration: const InputDecoration(
                    labelText: 'Nội dung',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: imageUrlController,
                  autofocus: false, // Explicitly disable autofocus
                  decoration: const InputDecoration(
                    labelText: 'URL hình ảnh (tùy chọn)',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<NotificationType>(
                  value: selectedType,
                  decoration: const InputDecoration(
                    labelText: 'Loại thông báo',
                    border: OutlineInputBorder(),
                  ),
                  items: NotificationType.values.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Row(
                        children: [
                          Icon(
                            type.icon,
                            color: type.color,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(type.displayName),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        selectedType = value;

                        // Cập nhật targetScreen dựa trên loại thông báo
                        switch (value) {
                          case NotificationType.movie:
                            targetScreenController.text = 'movie_detail';
                            break;
                          case NotificationType.ticket:
                            targetScreenController.text = 'ticket';
                            break;
                          case NotificationType.promo:
                            targetScreenController.text = 'promo';
                            break;
                          default:
                            targetScreenController.text = '';
                            break;
                        }
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: targetScreenController,
                  autofocus: false, // Explicitly disable autofocus
                  decoration: const InputDecoration(
                    labelText: 'Màn hình đích (tùy chọn)',
                    border: OutlineInputBorder(),
                    hintText: 'Ví dụ: movie_detail, ticket, promo',
                  ),
                ),
                const SizedBox(height: 16),
                ListTile(
                  title: const Text('Ngày hết hạn'),
                  subtitle: Text(
                    DateFormat('dd/MM/yyyy').format(expiresAt),
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  trailing: const Icon(Icons.calendar_today),
                  onTap: () async {
                    final pickedDate = await showDatePicker(
                      context: context,
                      initialDate: expiresAt,
                      firstDate: DateTime.now(),
                      lastDate: DateTime.now().add(const Duration(days: 365)),
                    );

                    if (pickedDate != null) {
                      setState(() {
                        expiresAt = pickedDate;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),
                SwitchListTile(
                  title: const Text('Thông báo công khai'),
                  subtitle: const Text('Hiển thị cho tất cả người dùng'),
                  value: isPublic,
                  onChanged: (value) {
                    setState(() {
                      isPublic = value;
                      if (isPublic) {
                        selectedUserIds.clear();
                      }
                    });
                  },
                ),
                if (!isPublic) ...[
                  const SizedBox(height: 16),
                  const Text(
                    'Chọn người nhận:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  isLoadingUsers
                      ? const Center(child: CircularProgressIndicator())
                      : Container(
                          height: 200,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: ListView.builder(
                            itemCount: users.length,
                            itemBuilder: (context, index) {
                              final user = users[index];
                              final isSelected =
                                  selectedUserIds.contains(user.id);

                              return CheckboxListTile(
                                title: Text(user.name ?? 'Unknown'),
                                subtitle: Text(user.email ?? 'No email'),
                                value: isSelected,
                                onChanged: (value) {
                                  setState(() {
                                    if (value == true) {
                                      if (user.id != null) {
                                        selectedUserIds.add(user.id!);
                                      }
                                    } else {
                                      selectedUserIds.remove(user.id);
                                    }
                                  });
                                },
                              );
                            },
                          ),
                        ),
                ],
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Hủy'),
            ),
            TextButton(
              onPressed: () async {
                if (titleController.text.isEmpty ||
                    bodyController.text.isEmpty) {
                  Get.snackbar(
                    'Error',
                    'Vui lòng nhập tiêu đề và nội dung',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.red,
                    colorText: Colors.white,
                  );
                  return;
                }

                if (!isPublic && selectedUserIds.isEmpty) {
                  Get.snackbar(
                    'Error',
                    'Vui lòng chọn ít nhất một người nhận',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.red,
                    colorText: Colors.white,
                  );
                  return;
                }

                try {
                  // Close the dialog before the async operation
                  Navigator.pop(context);

                  // Tạo dữ liệu bổ sung dựa trên loại thông báo
                  Map<String, dynamic>? data;
                  if (selectedType == NotificationType.movie) {
                    data = {'movieId': '1'}; // Giá trị mặc định
                  }

                  // Tạo thông báo mới
                  final notification =
                      await notificationService.createNotification(
                    title: titleController.text.trim(),
                    body: bodyController.text.trim(),
                    imageUrl: imageUrlController.text.isEmpty
                        ? null
                        : imageUrlController.text.trim(),
                    expiresAt: expiresAt,
                    targetScreen: targetScreenController.text.isEmpty
                        ? null
                        : targetScreenController.text.trim(),
                    data: data,
                    isPublic: isPublic,
                    targetUserIds: isPublic ? null : selectedUserIds,
                  );

                  if (notification != null) {
                    Get.snackbar(
                      'Thành công',
                      'Thông báo đã được thêm thành công',
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: Colors.green,
                      colorText: Colors.white,
                    );
                  } else {
                    Get.snackbar(
                      'Lỗi',
                      'Không thể tạo thông báo',
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: Colors.red,
                      colorText: Colors.white,
                    );
                  }

                  _loadNotifications();
                } catch (e) {
                  Get.snackbar(
                    'Lỗi',
                    'Thêm thông báo thất bại: $e',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.red,
                    colorText: Colors.white,
                  );
                }
              },
              child: const Text('Thêm'),
            ),
          ],
        ),
      ),
    );
  }
}
